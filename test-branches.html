<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار إدارة الفروع - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .admin-link {
            text-align: center;
            margin-top: 30px;
        }
        .admin-link a {
            background: #28a745;
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-size: 18px;
            transition: all 0.3s ease;
        }
        .admin-link a:hover {
            background: #218838;
            transform: translateY(-2px);
        }
    </style>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار نظام إدارة الفروع</h1>
        
        <div class="test-section">
            <h3>📋 معلومات النظام</h3>
            <p><strong>الهدف:</strong> اختبار إضافة وإدارة الفروع في Firebase Realtime Database</p>
            <p><strong>قاعدة البيانات:</strong> branches/</p>
            <p><strong>الوظائف:</strong> إضافة، تعديل، حذف، عرض الفروع</p>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار الاتصال</h3>
            <button class="btn" onclick="testConnection()">اختبار الاتصال بـ Firebase</button>
            <div id="connection-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>➕ إضافة فروع تجريبية</h3>
            <button class="btn" onclick="addSampleBranches()">إضافة فروع تجريبية</button>
            <div id="add-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>📊 عرض الفروع الحالية</h3>
            <button class="btn" onclick="loadBranches()">تحميل الفروع</button>
            <div id="branches-list"></div>
            <div id="load-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🗑️ مسح البيانات التجريبية</h3>
            <button class="btn" onclick="clearTestData()" style="background: #dc3545;">مسح جميع الفروع التجريبية</button>
            <div id="clear-status" class="status"></div>
        </div>

        <div class="admin-link">
            <a href="admin.html">🚀 الذهاب إلى لوحة الإدارة</a>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const database = firebase.database();

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
        }

        async function testConnection() {
            try {
                showStatus('connection-status', 'جاري اختبار الاتصال...', 'info');
                
                const testRef = database.ref('.info/connected');
                const snapshot = await testRef.once('value');
                
                if (snapshot.val() === true) {
                    showStatus('connection-status', '✅ الاتصال بـ Firebase ناجح!', 'success');
                } else {
                    showStatus('connection-status', '❌ فشل في الاتصال بـ Firebase', 'error');
                }
            } catch (error) {
                showStatus('connection-status', `❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }

        async function addSampleBranches() {
            try {
                showStatus('add-status', 'جاري إضافة الفروع التجريبية...', 'info');
                
                const sampleBranches = [
                    {
                        name: "فرع الرياض الرئيسي",
                        address: "شارع الملك فهد، حي العليا، الرياض 12211، المملكة العربية السعودية",
                        phone: "+966112345678"
                    },
                    {
                        name: "فرع جدة",
                        address: "طريق الملك عبدالعزيز، حي الروضة، جدة 23431، المملكة العربية السعودية",
                        phone: "+966126789012"
                    },
                    {
                        name: "فرع الدمام",
                        address: "شارع الأمير محمد بن فهد، حي الفيصلية، الدمام 32241، المملكة العربية السعودية",
                        phone: "+966138901234"
                    }
                ];

                for (let i = 0; i < sampleBranches.length; i++) {
                    const branchData = {
                        ...sampleBranches[i],
                        createdAt: new Date().toISOString(),
                        updatedAt: new Date().toISOString()
                    };
                    
                    const branchId = `test_branch_${Date.now()}_${i}`;
                    await database.ref(`branches/${branchId}`).set(branchData);
                }

                showStatus('add-status', `✅ تم إضافة ${sampleBranches.length} فروع تجريبية بنجاح!`, 'success');
                
            } catch (error) {
                showStatus('add-status', `❌ خطأ في إضافة الفروع: ${error.message}`, 'error');
            }
        }

        async function loadBranches() {
            try {
                showStatus('load-status', 'جاري تحميل الفروع...', 'info');
                
                const snapshot = await database.ref('branches').once('value');
                const branchesData = snapshot.val();
                
                const branchesList = document.getElementById('branches-list');
                
                if (branchesData && Object.keys(branchesData).length > 0) {
                    const branches = Object.entries(branchesData);
                    
                    let html = '<div style="margin-top: 15px;"><h4>الفروع الموجودة:</h4>';
                    branches.forEach(([id, branch]) => {
                        html += `
                            <div style="background: white; padding: 15px; margin: 10px 0; border-radius: 8px; border: 1px solid #ddd;">
                                <strong>${branch.name}</strong><br>
                                <small style="color: #666;">📍 ${branch.address}</small><br>
                                ${branch.phone ? `<small style="color: #666;">📞 ${branch.phone}</small><br>` : ''}
                                <small style="color: #999;">🆔 ${id}</small>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    branchesList.innerHTML = html;
                    showStatus('load-status', `✅ تم تحميل ${branches.length} فرع`, 'success');
                } else {
                    branchesList.innerHTML = '<p style="text-align: center; color: #666; margin-top: 15px;">لا توجد فروع مضافة</p>';
                    showStatus('load-status', 'ℹ️ لا توجد فروع في قاعدة البيانات', 'info');
                }
                
            } catch (error) {
                showStatus('load-status', `❌ خطأ في تحميل الفروع: ${error.message}`, 'error');
            }
        }

        async function clearTestData() {
            if (!confirm('هل تريد حذف جميع الفروع؟ هذا الإجراء لا يمكن التراجع عنه.')) {
                return;
            }
            
            try {
                showStatus('clear-status', 'جاري حذف البيانات...', 'info');
                
                await database.ref('branches').remove();
                
                document.getElementById('branches-list').innerHTML = '';
                showStatus('clear-status', '✅ تم حذف جميع الفروع بنجاح', 'success');
                
            } catch (error) {
                showStatus('clear-status', `❌ خطأ في حذف البيانات: ${error.message}`, 'error');
            }
        }

        // Test connection on page load
        window.addEventListener('load', testConnection);
    </script>
</body>
</html>
