<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الاختبار النهائي - عرض الفروع</title>
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <!-- Dynamic Content Manager -->
    <script src="dynamic-content.js"></script>
    
    <!-- Branches Display Fix -->
    <script src="branches-display-fix.js"></script>
    
    <style>
        body {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            justify-content: center;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: bold;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn.success { background: linear-gradient(45deg, #28a745, #20c997); }
        .btn.warning { background: linear-gradient(45deg, #ffc107, #fd7e14); }
        .btn.danger { background: linear-gradient(45deg, #dc3545, #e83e8c); }
        
        .status-panel {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .status-item.success { background: #d4edda; color: #155724; }
        .status-item.error { background: #f8d7da; color: #721c24; }
        .status-item.warning { background: #fff3cd; color: #856404; }
        .status-item.info { background: #d1ecf1; color: #0c5460; }
        
        .icon {
            margin-left: 10px;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="header">
            <h1>🎯 الاختبار النهائي لعرض الفروع</h1>
            <p>اختبار شامل لضمان عمل نظام عرض الفروع بشكل مثالي</p>
        </div>
        
        <div class="controls">
            <button class="btn" onclick="runFullTest()">🧪 تشغيل الاختبار الشامل</button>
            <button class="btn success" onclick="window.branchesDisplayFix.refresh()">🔄 تحديث الفروع</button>
            <button class="btn warning" onclick="checkAllSystems()">🔍 فحص جميع الأنظمة</button>
            <button class="btn danger" onclick="clearAndReload()">🗑️ مسح وإعادة تحميل</button>
        </div>
        
        <div class="status-panel">
            <h3>📊 حالة الأنظمة</h3>
            <div id="status-list"></div>
        </div>
        
        <!-- قسم الفروع الفعلي -->
        <section id="branches" class="branches-section">
            <h2 class="branches-title">🏢 فروعنا</h2>
            <div class="branches-grid" id="dynamic-branches">
                <div class="no-data-message" id="no-branches-message">
                    <p>لا توجد فروع مضافة حالياً. يرجى إضافة الفروع من لوحة الإدارة.</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        console.log('🔥 Firebase initialized');

        function addStatus(message, type, icon) {
            const statusList = document.getElementById('status-list');
            const statusItem = document.createElement('div');
            statusItem.className = `status-item ${type}`;
            statusItem.innerHTML = `<span class="icon">${icon}</span>${message}`;
            statusList.appendChild(statusItem);
        }

        function clearStatus() {
            document.getElementById('status-list').innerHTML = '';
        }

        async function checkFirebase() {
            try {
                const database = firebase.database();
                const testRef = database.ref('.info/connected');
                const snapshot = await testRef.once('value');
                
                if (snapshot.val() === true) {
                    addStatus('Firebase متصل بنجاح', 'success', '✅');
                    return true;
                } else {
                    addStatus('Firebase غير متصل', 'error', '❌');
                    return false;
                }
            } catch (error) {
                addStatus(`خطأ في Firebase: ${error.message}`, 'error', '❌');
                return false;
            }
        }

        async function checkBranchesData() {
            try {
                const database = firebase.database();
                const snapshot = await database.ref('branches').once('value');
                const data = snapshot.val();
                
                if (data && Object.keys(data).length > 0) {
                    addStatus(`تم العثور على ${Object.keys(data).length} فرع في قاعدة البيانات`, 'success', '🏢');
                    return data;
                } else {
                    addStatus('لا توجد فروع في قاعدة البيانات', 'warning', '⚠️');
                    return null;
                }
            } catch (error) {
                addStatus(`خطأ في تحميل الفروع: ${error.message}`, 'error', '❌');
                return null;
            }
        }

        function checkDOMElements() {
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');
            
            if (branchesGrid) {
                addStatus('عنصر الفروع موجود في DOM', 'success', '📍');
                
                const branchCards = branchesGrid.querySelectorAll('.branch-card');
                if (branchCards.length > 0) {
                    addStatus(`يتم عرض ${branchCards.length} فرع في الصفحة`, 'success', '🎯');
                } else {
                    addStatus('لا يتم عرض أي فروع في الصفحة', 'warning', '⚠️');
                }
            } else {
                addStatus('عنصر الفروع غير موجود في DOM', 'error', '❌');
            }
            
            if (noDataMessage) {
                const isVisible = noDataMessage.style.display !== 'none' && !noDataMessage.classList.contains('hidden');
                addStatus(`رسالة "لا توجد بيانات" ${isVisible ? 'ظاهرة' : 'مخفية'}`, 'info', '👁️');
            }
        }

        function checkManagers() {
            if (window.dynamicContentManager) {
                addStatus('Dynamic Content Manager متاح', 'success', '🔧');
            } else {
                addStatus('Dynamic Content Manager غير متاح', 'warning', '⚠️');
            }
            
            if (window.simpleBranchesManager) {
                addStatus('Simple Branches Manager متاح', 'success', '🔧');
            } else {
                addStatus('Simple Branches Manager غير متاح', 'warning', '⚠️');
            }
            
            if (window.branchesDisplayFix && window.branchesDisplayFix.isReady()) {
                addStatus('Branches Display Fix جاهز', 'success', '🛠️');
            } else {
                addStatus('Branches Display Fix غير جاهز', 'warning', '⚠️');
            }
        }

        async function runFullTest() {
            clearStatus();
            addStatus('بدء الاختبار الشامل...', 'info', '🚀');
            
            // Test Firebase
            const firebaseOK = await checkFirebase();
            
            // Test branches data
            const branchesData = await checkBranchesData();
            
            // Test DOM elements
            checkDOMElements();
            
            // Test managers
            checkManagers();
            
            // Final result
            if (firebaseOK && branchesData) {
                addStatus('جميع الاختبارات مكتملة - النظام يعمل بشكل صحيح', 'success', '🎉');
            } else {
                addStatus('هناك مشاكل تحتاج إلى إصلاح', 'error', '🔧');
            }
        }

        async function checkAllSystems() {
            clearStatus();
            addStatus('فحص جميع الأنظمة...', 'info', '🔍');
            
            await runFullTest();
            
            // Additional checks
            setTimeout(() => {
                const branchCards = document.querySelectorAll('.branch-card');
                if (branchCards.length > 0) {
                    addStatus('✅ الفروع تظهر بشكل صحيح!', 'success', '🎯');
                } else {
                    addStatus('❌ الفروع لا تظهر - يحتاج إصلاح', 'error', '🔧');
                }
            }, 2000);
        }

        async function clearAndReload() {
            clearStatus();
            addStatus('مسح وإعادة تحميل...', 'info', '🔄');
            
            // Clear existing branches
            const branchesGrid = document.getElementById('dynamic-branches');
            if (branchesGrid) {
                const existingCards = branchesGrid.querySelectorAll('.branch-card');
                existingCards.forEach(card => card.remove());
                addStatus(`تم مسح ${existingCards.length} بطاقة فرع`, 'info', '🗑️');
            }
            
            // Force reload
            if (window.branchesDisplayFix) {
                await window.branchesDisplayFix.refresh();
                addStatus('تم إعادة تحميل الفروع', 'success', '🔄');
            }
            
            // Check result
            setTimeout(() => {
                checkDOMElements();
            }, 1000);
        }

        // Auto-run test when page loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                runFullTest();
            }, 2000);
        });
    </script>
</body>
</html>
