<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة الفروع - AL-SALAMAT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            direction: rtl;
            padding: 20px;
            background: #f5f7fa;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        h1 {
            color: #667eea;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .test-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
        .branches-preview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }
        .branch-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .branch-card h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
        .branch-card p {
            color: #666;
            margin: 5px 0;
        }
    </style>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <!-- Dynamic Content Manager -->
    <script src="dynamic-content.js"></script>
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة عرض الفروع</h1>
        
        <div class="test-section">
            <h3>📋 معلومات التشخيص</h3>
            <p><strong>الهدف:</strong> تحديد سبب عدم ظهور الفروع في الصفحة الرئيسية</p>
            <p><strong>المشكلة:</strong> الفروع تُضاف في Firebase لكن لا تظهر في الموقع</p>
        </div>

        <div class="test-section">
            <h3>🔧 اختبار Firebase</h3>
            <button class="btn" onclick="testFirebaseConnection()">اختبار الاتصال</button>
            <div id="firebase-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>📊 اختبار تحميل الفروع</h3>
            <button class="btn" onclick="testLoadBranches()">تحميل الفروع من Firebase</button>
            <div id="load-status" class="status"></div>
            <div id="branches-data"></div>
        </div>

        <div class="test-section">
            <h3>🎯 اختبار Dynamic Content Manager</h3>
            <button class="btn" onclick="testDynamicManager()">اختبار المدير الديناميكي</button>
            <div id="dynamic-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>🔍 اختبار عناصر DOM</h3>
            <button class="btn" onclick="testDOMElements()">فحص عناصر الصفحة</button>
            <div id="dom-status" class="status"></div>
        </div>

        <div class="test-section">
            <h3>📝 سجل الأحداث</h3>
            <button class="btn" onclick="clearLog()">مسح السجل</button>
            <div id="console-log" class="log-area"></div>
        </div>

        <div class="test-section">
            <h3>🏢 محاكاة قسم الفروع</h3>
            <p>هذا هو نفس العنصر الموجود في الصفحة الرئيسية:</p>
            <div class="branches-grid" id="dynamic-branches">
                <div class="no-data-message" id="no-branches-message">
                    <p>لا توجد فروع مضافة حالياً. يرجى إضافة الفروع من لوحة الإدارة.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        if (!firebase.apps.length) {
            firebase.initializeApp(firebaseConfig);
        }
        const database = firebase.database();

        // Logging function
        function log(message, type = 'info') {
            const logArea = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(message);
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.style.display = 'block';
            log(message, type);
        }

        function clearLog() {
            document.getElementById('console-log').textContent = '';
        }

        async function testFirebaseConnection() {
            try {
                showStatus('firebase-status', 'جاري اختبار الاتصال...', 'info');
                
                const testRef = database.ref('.info/connected');
                const snapshot = await testRef.once('value');
                
                if (snapshot.val() === true) {
                    showStatus('firebase-status', '✅ الاتصال بـ Firebase ناجح!', 'success');
                } else {
                    showStatus('firebase-status', '❌ فشل في الاتصال بـ Firebase', 'error');
                }
            } catch (error) {
                showStatus('firebase-status', `❌ خطأ في الاتصال: ${error.message}`, 'error');
            }
        }

        async function testLoadBranches() {
            try {
                showStatus('load-status', 'جاري تحميل الفروع...', 'info');
                
                const snapshot = await database.ref('branches').once('value');
                const branchesData = snapshot.val();
                
                const branchesDataDiv = document.getElementById('branches-data');
                
                if (branchesData && Object.keys(branchesData).length > 0) {
                    const branches = Object.entries(branchesData);
                    
                    let html = '<div class="branches-preview">';
                    branches.forEach(([id, branch]) => {
                        html += `
                            <div class="branch-card">
                                <h4>${branch.name}</h4>
                                <p><strong>العنوان:</strong> ${branch.address}</p>
                                <p><strong>الهاتف:</strong> ${branch.phone || 'غير محدد'}</p>
                                <p><strong>المعرف:</strong> ${id}</p>
                                <p><strong>تاريخ الإنشاء:</strong> ${new Date(branch.createdAt).toLocaleString('ar-SA')}</p>
                            </div>
                        `;
                    });
                    html += '</div>';
                    
                    branchesDataDiv.innerHTML = html;
                    showStatus('load-status', `✅ تم تحميل ${branches.length} فرع بنجاح`, 'success');
                    
                    // Test the dynamic content manager update
                    if (window.DynamicContentManager) {
                        log('محاولة تحديث الفروع باستخدام Dynamic Content Manager...', 'info');
                        const manager = new DynamicContentManager();
                        setTimeout(() => {
                            manager.updateBranches(branchesData);
                        }, 1000);
                    }
                    
                } else {
                    branchesDataDiv.innerHTML = '<p style="text-align: center; color: #666;">لا توجد فروع في قاعدة البيانات</p>';
                    showStatus('load-status', 'ℹ️ لا توجد فروع في قاعدة البيانات', 'warning');
                }
                
            } catch (error) {
                showStatus('load-status', `❌ خطأ في تحميل الفروع: ${error.message}`, 'error');
            }
        }

        function testDynamicManager() {
            try {
                log('فحص Dynamic Content Manager...', 'info');
                
                if (typeof DynamicContentManager !== 'undefined') {
                    showStatus('dynamic-status', '✅ Dynamic Content Manager موجود', 'success');
                    log('DynamicContentManager class found', 'success');
                    
                    // Check if instance exists
                    if (window.dynamicContentManager) {
                        log('Instance موجود في window.dynamicContentManager', 'success');
                    } else {
                        log('لا يوجد instance في window.dynamicContentManager', 'warning');
                        log('محاولة إنشاء instance جديد...', 'info');
                        window.dynamicContentManager = new DynamicContentManager();
                    }
                } else {
                    showStatus('dynamic-status', '❌ Dynamic Content Manager غير موجود!', 'error');
                    log('DynamicContentManager class not found!', 'error');
                }
            } catch (error) {
                showStatus('dynamic-status', `❌ خطأ في Dynamic Content Manager: ${error.message}`, 'error');
                log(`Error testing Dynamic Content Manager: ${error.message}`, 'error');
            }
        }

        function testDOMElements() {
            try {
                log('فحص عناصر DOM...', 'info');
                
                const branchesGrid = document.getElementById('dynamic-branches');
                const noDataMessage = document.getElementById('no-branches-message');
                
                let status = '';
                
                if (branchesGrid) {
                    status += '✅ عنصر dynamic-branches موجود\n';
                    log('Element #dynamic-branches found', 'success');
                    
                    const existingCards = branchesGrid.querySelectorAll('.branch-card');
                    status += `📊 عدد بطاقات الفروع: ${existingCards.length}\n`;
                    log(`Found ${existingCards.length} branch cards`, 'info');
                } else {
                    status += '❌ عنصر dynamic-branches غير موجود!\n';
                    log('Element #dynamic-branches NOT found!', 'error');
                }
                
                if (noDataMessage) {
                    status += '✅ عنصر no-branches-message موجود\n';
                    const isHidden = noDataMessage.classList.contains('hidden');
                    status += `👁️ رسالة "لا توجد بيانات" ${isHidden ? 'مخفية' : 'ظاهرة'}\n`;
                    log(`No data message visibility: ${isHidden ? 'hidden' : 'visible'}`, 'info');
                } else {
                    status += '❌ عنصر no-branches-message غير موجود!\n';
                    log('Element #no-branches-message NOT found!', 'error');
                }
                
                showStatus('dom-status', status, 'info');
                
            } catch (error) {
                showStatus('dom-status', `❌ خطأ في فحص DOM: ${error.message}`, 'error');
                log(`Error testing DOM elements: ${error.message}`, 'error');
            }
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            log('صفحة التشخيص تم تحميلها', 'info');
            
            // Auto-run tests
            setTimeout(() => {
                testFirebaseConnection();
                setTimeout(() => {
                    testDynamicManager();
                    setTimeout(() => {
                        testDOMElements();
                        setTimeout(() => {
                            testLoadBranches();
                        }, 1000);
                    }, 1000);
                }, 1000);
            }, 1000);
        });
    </script>
</body>
</html>
