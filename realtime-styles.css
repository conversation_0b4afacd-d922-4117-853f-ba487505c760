/* Enhanced Real-time Updates Styles for AL-SALAMAT */

/* Real-time update notifications */
.realtime-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    z-index: 10000;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-right: 4px solid #2E7D32;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    direction: rtl;
    text-align: right;
}

.realtime-notification.show {
    transform: translateX(0);
}

/* Enhanced update indicator */
.update-indicator {
    position: fixed;
    top: 10px;
    left: 50%;
    transform: translateX(-50%) translateY(-100%);
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 10px 20px;
    border-radius: 25px;
    z-index: 9999;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: transform 0.3s ease;
    direction: rtl;
    text-align: center;
    min-width: 200px;
}

.update-indicator.show {
    transform: translateX(-50%) translateY(0);
}

/* Real-time sync status indicator */
.sync-status {
    position: fixed;
    bottom: 20px;
    left: 20px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 9998;
    transition: all 0.3s ease;
    direction: rtl;
}

.sync-status.online {
    background: #4CAF50;
    color: white;
}

.sync-status.offline {
    background: #f44336;
    color: white;
}

.sync-status.syncing {
    background: #ff9800;
    color: white;
    animation: pulse 1.5s infinite;
}

/* Pulse animation for syncing status */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 152, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
    }
}

/* Enhanced animations for updated elements */
.realtime-updated {
    animation: realtimeUpdate 0.6s ease;
}

@keyframes realtimeUpdate {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0);
    }
    50% {
        transform: scale(1.02);
        box-shadow: 0 0 20px rgba(76, 175, 80, 0.4);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* Loading overlay for real-time updates */
.realtime-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.1);
    z-index: 9997;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.realtime-loading.show {
    opacity: 1;
    visibility: visible;
}

.realtime-loading .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Enhanced branch cards with real-time updates */
.branch-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.branch-card.realtime-new {
    animation: slideInFromRight 0.5s ease;
}

.branch-card.realtime-updated {
    animation: highlightUpdate 0.8s ease;
}

.branch-card.realtime-removed {
    animation: slideOutToLeft 0.5s ease;
}

@keyframes slideInFromRight {
    0% {
        transform: translateX(100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutToLeft {
    0% {
        transform: translateX(0);
        opacity: 1;
    }
    100% {
        transform: translateX(-100%);
        opacity: 0;
    }
}

@keyframes highlightUpdate {
    0% {
        background: inherit;
        transform: scale(1);
    }
    50% {
        background: rgba(76, 175, 80, 0.1);
        transform: scale(1.02);
    }
    100% {
        background: inherit;
        transform: scale(1);
    }
}

/* Real-time connection status bar */
.connection-status {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 8px;
    text-align: center;
    font-size: 12px;
    font-weight: 500;
    z-index: 10001;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
    direction: rtl;
}

.connection-status.show {
    transform: translateY(0);
}

.connection-status.connected {
    background: #4CAF50;
    color: white;
}

.connection-status.disconnected {
    background: #f44336;
    color: white;
}

.connection-status.reconnecting {
    background: #ff9800;
    color: white;
}

/* Enhanced gallery items with real-time updates */
.gallery-item {
    transition: all 0.3s ease;
}

.gallery-item.realtime-new {
    animation: fadeInScale 0.6s ease;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Real-time update progress bar */
.update-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    z-index: 10002;
    transition: width 0.3s ease;
}

.update-progress.show {
    width: 100%;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .realtime-notification {
        top: 10px;
        right: 10px;
        left: 10px;
        font-size: 13px;
        padding: 10px 15px;
    }
    
    .update-indicator {
        top: 5px;
        font-size: 13px;
        padding: 8px 15px;
        min-width: 150px;
    }
    
    .sync-status {
        bottom: 10px;
        left: 10px;
        font-size: 11px;
        padding: 6px 12px;
    }
}

/* Dark mode support for real-time updates */
@media (prefers-color-scheme: dark) {
    .realtime-notification {
        background: linear-gradient(135deg, #2E7D32, #1B5E20);
        border-right-color: #4CAF50;
    }
    
    .update-indicator {
        background: linear-gradient(135deg, #3F51B5, #303F9F);
    }
    
    .connection-status.connected {
        background: #2E7D32;
    }
    
    .connection-status.disconnected {
        background: #C62828;
    }
    
    .connection-status.reconnecting {
        background: #F57C00;
    }
}

/* Accessibility improvements */
.realtime-notification,
.update-indicator,
.sync-status,
.connection-status {
    font-family: inherit;
    line-height: 1.4;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .realtime-notification,
    .update-indicator,
    .branch-card,
    .gallery-item {
        animation: none;
        transition: none;
    }
    
    .realtime-updated {
        animation: none;
    }
    
    .sync-status.syncing {
        animation: none;
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .realtime-notification {
        border: 2px solid white;
    }
    
    .update-indicator {
        border: 2px solid white;
    }
    
    .sync-status {
        border: 1px solid white;
    }
}
