<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الربط - AL-SALAMAT</title>
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        .status-online {
            background: #28a745;
        }
        .status-offline {
            background: #dc3545;
        }
        .status-unknown {
            background: #ffc107;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>اختبار الربط بين الصفحة الرئيسية ولوحة الإدارة</h1>
        
        <div class="test-section">
            <h2>🔧 حالة النظام</h2>
            <div id="system-status">
                <p><span class="status-indicator status-unknown"></span>Firebase: جاري الفحص...</p>
                <p><span class="status-indicator status-unknown"></span>مدير المحتوى الديناميكي: جاري الفحص...</p>
                <p><span class="status-indicator status-unknown"></span>مستمعات Firebase: جاري الفحص...</p>
            </div>
            <button onclick="checkSystemStatus()">فحص حالة النظام</button>
        </div>

        <div class="test-section">
            <h2>📝 اختبار قسم "من نحن"</h2>
            <button onclick="testAboutSection()">اختبار تحديث قسم "من نحن"</button>
            <div id="about-test-results"></div>
        </div>

        <div class="test-section">
            <h2>📞 اختبار قسم "اتصل بنا"</h2>
            <button onclick="testContactSection()">اختبار تحديث قسم "اتصل بنا"</button>
            <div id="contact-test-results"></div>
        </div>

        <div class="test-section">
            <h2>🏢 اختبار الفروع</h2>
            <button onclick="testBranches()">اختبار إضافة فرع</button>
            <button onclick="clearAllBranches()">حذف جميع الفروع</button>
            <div id="branches-test-results"></div>
        </div>

        <div class="test-section">
            <h2>🖼️ اختبار المعرض</h2>
            <button onclick="testGallery()">اختبار إضافة صورة</button>
            <div id="gallery-test-results"></div>
        </div>

        <div class="test-section">
            <h2>📊 نتائج الاختبار الشامل</h2>
            <button onclick="runFullTest()">تشغيل اختبار شامل</button>
            <div id="full-test-results"></div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        let database;
        let testResults = {};

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function updateStatus(service, status) {
            const statusDiv = document.getElementById('system-status');
            const statusText = statusDiv.innerHTML;
            const statusClass = status === 'online' ? 'status-online' : status === 'offline' ? 'status-offline' : 'status-unknown';
            const newStatusText = statusText.replace(
                new RegExp(`(<span class="status-indicator[^"]*"></span>${service}:)[^<]*`),
                `<span class="status-indicator ${statusClass}"></span>${service}: ${status === 'online' ? 'متصل ✅' : status === 'offline' ? 'غير متصل ❌' : 'غير معروف ⚠️'}`
            );
            statusDiv.innerHTML = newStatusText;
        }

        async function checkSystemStatus() {
            try {
                // Initialize Firebase
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                }
                database = firebase.database();
                updateStatus('Firebase', 'online');

                // Test connection
                const testRef = database.ref('.info/connected');
                testRef.on('value', (snapshot) => {
                    if (snapshot.val() === true) {
                        updateStatus('Firebase', 'online');
                    } else {
                        updateStatus('Firebase', 'offline');
                    }
                });

                // Check if dynamic content manager exists
                if (typeof DynamicContentManager !== 'undefined') {
                    updateStatus('مدير المحتوى الديناميكي', 'online');
                } else {
                    updateStatus('مدير المحتوى الديناميكي', 'offline');
                }

                // Test listeners
                const testListener = database.ref('test');
                testListener.on('value', () => {
                    updateStatus('مستمعات Firebase', 'online');
                });
                
                // Trigger test
                await testListener.set({ test: true });
                await testListener.remove();

            } catch (error) {
                console.error('Error checking system status:', error);
                updateStatus('Firebase', 'offline');
            }
        }

        async function testAboutSection() {
            try {
                addResult('about-test-results', 'بدء اختبار قسم "من نحن"...', 'info');
                
                const testData = {
                    title: `من نحن (اختبار ${new Date().toLocaleTimeString()})`,
                    description: `هذا نص اختبار لقسم "من نحن" تم تحديثه في ${new Date().toLocaleString()}. يجب أن يظهر هذا النص في الصفحة الرئيسية فوراً.`,
                    updatedAt: new Date().toISOString()
                };

                await database.ref('aboutSection').set(testData);
                addResult('about-test-results', '✅ تم حفظ البيانات في Firebase', 'success');
                addResult('about-test-results', '🔍 تحقق من الصفحة الرئيسية للتأكد من التحديث', 'warning');
                
            } catch (error) {
                addResult('about-test-results', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function testContactSection() {
            try {
                addResult('contact-test-results', 'بدء اختبار قسم "اتصل بنا"...', 'info');
                
                const testData = {
                    title: `اتصل بنا (اختبار ${new Date().toLocaleTimeString()})`,
                    infoTitle: `معلومات التواصل (محدث ${new Date().toLocaleTimeString()})`,
                    address: `عنوان اختبار - ${new Date().toLocaleDateString()}`,
                    hours: `ساعات عمل محدثة - ${new Date().toLocaleTimeString()}`,
                    updatedAt: new Date().toISOString()
                };

                await database.ref('contactSection').set(testData);
                addResult('contact-test-results', '✅ تم حفظ البيانات في Firebase', 'success');
                addResult('contact-test-results', '🔍 تحقق من الصفحة الرئيسية للتأكد من التحديث', 'warning');
                
            } catch (error) {
                addResult('contact-test-results', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function testBranches() {
            try {
                addResult('branches-test-results', 'بدء اختبار إضافة فرع...', 'info');
                
                const testBranch = {
                    name: `فرع اختبار ${new Date().getTime()}`,
                    address: `عنوان اختبار - ${new Date().toLocaleDateString()}`,
                    phone: '+966500000000',
                    createdAt: new Date().toISOString()
                };

                const branchRef = database.ref('branches').push();
                await branchRef.set(testBranch);
                
                addResult('branches-test-results', `✅ تم إضافة فرع بمعرف: ${branchRef.key}`, 'success');
                addResult('branches-test-results', '🔍 تحقق من الصفحة الرئيسية للتأكد من ظهور الفرع', 'warning');
                
            } catch (error) {
                addResult('branches-test-results', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function clearAllBranches() {
            try {
                addResult('branches-test-results', 'حذف جميع الفروع...', 'info');
                await database.ref('branches').remove();
                addResult('branches-test-results', '✅ تم حذف جميع الفروع', 'success');
                addResult('branches-test-results', '🔍 يجب أن تظهر رسالة "لا توجد فروع" في الصفحة الرئيسية', 'warning');
            } catch (error) {
                addResult('branches-test-results', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function testGallery() {
            try {
                addResult('gallery-test-results', 'بدء اختبار إضافة صورة...', 'info');
                
                const testImage = {
                    url: 'https://via.placeholder.com/300x200?text=Test+Image',
                    alt: `صورة اختبار ${new Date().toLocaleTimeString()}`,
                    createdAt: new Date().toISOString()
                };

                const imageRef = database.ref('gallery').push();
                await imageRef.set(testImage);
                
                addResult('gallery-test-results', `✅ تم إضافة صورة بمعرف: ${imageRef.key}`, 'success');
                addResult('gallery-test-results', '🔍 تحقق من الصفحة الرئيسية للتأكد من ظهور الصورة', 'warning');
                
            } catch (error) {
                addResult('gallery-test-results', `❌ خطأ: ${error.message}`, 'error');
            }
        }

        async function runFullTest() {
            addResult('full-test-results', '🚀 بدء الاختبار الشامل...', 'info');
            
            try {
                await checkSystemStatus();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testAboutSection();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testContactSection();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testBranches();
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                await testGallery();
                
                addResult('full-test-results', '✅ تم إنجاز الاختبار الشامل', 'success');
                addResult('full-test-results', '🔍 تحقق من الصفحة الرئيسية للتأكد من جميع التحديثات', 'warning');
                
            } catch (error) {
                addResult('full-test-results', `❌ خطأ في الاختبار الشامل: ${error.message}`, 'error');
            }
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
