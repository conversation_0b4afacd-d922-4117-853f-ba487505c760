# دليل استخدام نظام إدارة الفروع 🏢

## نظرة عامة
تم إضافة نظام إدارة الفروع الجديد إلى لوحة الإدارة في موقع AL-SALAMAT. يمكن للمديرين الآن إضافة وتعديل وحذف الفروع بسهولة، مع عرض التحديثات فوراً في الصفحة الرئيسية.

## الوصول إلى نظام إدارة الفروع

### 1. تسجيل الدخول
- اذهب إلى صفحة تسجيل الدخول: `login.html`
- استخدم حساب المدير: `<EMAIL>`
- أدخل كلمة المرور الخاصة بك

### 2. الوصول للوحة الإدارة
- بعد تسجيل الدخول، انقر على "لوحة الإدارة" في القائمة
- أو اذهب مباشرة إلى: `admin.html`

### 3. فتح قسم إدارة الفروع
- في القائمة الجانبية، انقر على "إدارة الفروع"
- ستظهر لك صفحة إدارة الفروع الكاملة

## إضافة فرع جديد ➕

### الخطوات:
1. **املأ النموذج** في قسم "إضافة فرع جديد":
   - **اسم الفرع** (مطلوب): مثل "فرع الرياض الرئيسي"
   - **عنوان الفرع** (مطلوب): العنوان الكامل والمفصل
   - **رقم الهاتف** (اختياري): رقم التواصل مع الفرع

2. **انقر على "إضافة الفرع"**

3. **تأكيد النجاح**: ستظهر رسالة تأكيد خضراء

### مثال على البيانات:
```
اسم الفرع: فرع الرياض الرئيسي
العنوان: شارع الملك فهد، حي العليا، الرياض 12211، المملكة العربية السعودية
الهاتف: +966112345678
```

## تعديل فرع موجود ✏️

### الخطوات:
1. **ابحث عن الفرع** في جدول "قائمة الفروع"
2. **انقر على زر "تعديل"** الأخضر
3. **عدّل البيانات** في النافذة المنبثقة
4. **انقر على "حفظ التغييرات"**
5. **تأكيد النجاح**: ستظهر رسالة تأكيد

### ملاحظات:
- يمكن تعديل جميع البيانات عدا تاريخ الإنشاء
- التغييرات تظهر فوراً في الصفحة الرئيسية

## حذف فرع 🗑️

### الخطوات:
1. **ابحث عن الفرع** في جدول "قائمة الفروع"
2. **انقر على زر "حذف"** الأحمر
3. **أكد الحذف** في رسالة التأكيد
4. **تأكيد النجاح**: ستظهر رسالة تأكيد

### تحذير:
⚠️ **الحذف نهائي ولا يمكن التراجع عنه!**

## عرض الفروع 📋

### في لوحة الإدارة:
- جدول منظم يعرض جميع الفروع
- يتضمن: الاسم، العنوان، الهاتف، تاريخ الإضافة
- أزرار التعديل والحذف لكل فرع

### في الصفحة الرئيسية:
- قسم "فروعنا" يعرض جميع الفروع
- كل فرع يحتوي على: الاسم، العنوان، رابط الموقع
- التحديثات تظهر فوراً بدون إعادة تحميل

## التحقق من صحة البيانات ✅

### القواعد المطبقة:
- **اسم الفرع**: مطلوب، أكثر من حرفين
- **العنوان**: مطلوب، أكثر من 10 أحرف
- **الهاتف**: اختياري، يمكن تركه فارغاً

### رسائل الخطأ:
- "يرجى ملء جميع الحقول المطلوبة"
- "اسم الفرع يجب أن يكون أكثر من حرفين"
- "عنوان الفرع يجب أن يكون أكثر تفصيلاً"

## اختبار النظام 🧪

### ملف الاختبار:
- **الملف**: `test-branches.html`
- **الاستخدام**: اختبار النظام قبل الاستخدام الفعلي

### وظائف الاختبار:
1. **اختبار الاتصال**: التأكد من الاتصال بـ Firebase
2. **إضافة فروع تجريبية**: إضافة 3 فروع للاختبار
3. **عرض الفروع**: عرض جميع الفروع الموجودة
4. **مسح البيانات**: حذف جميع الفروع التجريبية

## نصائح للاستخدام الأمثل 💡

### أفضل الممارسات:
1. **استخدم أسماء واضحة**: مثل "فرع الرياض الرئيسي" بدلاً من "فرع 1"
2. **اكتب عناوين مفصلة**: تتضمن الشارع والحي والمدينة
3. **أضف أرقام الهواتف**: لتسهيل التواصل مع العملاء
4. **راجع البيانات**: قبل الحفظ للتأكد من صحتها

### تجنب الأخطاء:
- ❌ لا تستخدم أسماء مكررة للفروع
- ❌ لا تترك العنوان مختصراً جداً
- ❌ لا تحذف الفروع بدون تأكد

## الدعم الفني 🛠️

### في حالة وجود مشاكل:
1. **تحقق من الاتصال بالإنترنت**
2. **أعد تحميل الصفحة**
3. **تأكد من صلاحيات المدير**
4. **استخدم ملف الاختبار للتشخيص**

### معلومات تقنية:
- **قاعدة البيانات**: Firebase Realtime Database
- **المسار**: `branches/`
- **التحديث**: فوري في الوقت الفعلي
- **الأمان**: حماية من XSS والهجمات الشائعة

---

## ملخص سريع 📝

1. **الدخول**: `admin.html` → "إدارة الفروع"
2. **الإضافة**: املأ النموذج → "إضافة الفرع"
3. **التعديل**: "تعديل" → عدّل البيانات → "حفظ التغييرات"
4. **الحذف**: "حذف" → تأكيد الحذف
5. **المراجعة**: تحقق من الصفحة الرئيسية

**تم إنشاء هذا الدليل في ديسمبر 2024 - AL-SALAMAT**
