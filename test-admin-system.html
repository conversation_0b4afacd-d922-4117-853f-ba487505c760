<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام الأدمن - AL-SALAMAT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .test-section {
            margin: 2rem 0;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 10px;
            border-right: 4px solid #667eea;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-success { background: #28a745; }
        .status-error { background: #dc3545; }
        .status-warning { background: #ffc107; }
        
        .instructions {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار نظام الأدمن الجديد</h1>
            <p>اختبار شامل لجميع وظائف لوحة الإدارة والتحديثات اللحظية</p>
        </div>

        <div class="instructions">
            <h3>📋 تعليمات الاختبار:</h3>
            <ol>
                <li>تأكد من تسجيل الدخول بحساب <EMAIL></li>
                <li>افتح لوحة الأدمن في تبويب منفصل</li>
                <li>افتح الصفحة الرئيسية في تبويب آخر</li>
                <li>اختبر إضافة/تعديل/حذف البيانات ومراقبة التحديث الفوري</li>
            </ol>
        </div>

        <!-- Firebase Connection Test -->
        <div class="test-section">
            <h3>🔥 اختبار اتصال Firebase</h3>
            <button class="test-button" onclick="testFirebaseConnection()">فحص الاتصال</button>
            <button class="test-button" onclick="testDatabaseRules()">فحص قواعد قاعدة البيانات</button>
            <div class="test-result" id="firebase-result">انقر على "فحص الاتصال" للبدء...</div>
        </div>

        <!-- Admin Panel Test -->
        <div class="test-section">
            <h3>🛠️ اختبار لوحة الأدمن</h3>
            <button class="test-button" onclick="openAdminPanel()">فتح لوحة الأدمن</button>
            <button class="test-button" onclick="testAdminFunctions()">اختبار الوظائف</button>
            <div class="test-result" id="admin-result">انقر على "فتح لوحة الأدمن" للبدء...</div>
        </div>

        <!-- Real-time Updates Test -->
        <div class="test-section">
            <h3>⚡ اختبار التحديثات اللحظية</h3>
            <button class="test-button" onclick="testRealtimeUpdates()">اختبار التحديثات</button>
            <button class="test-button" onclick="addTestBranch()">إضافة فرع تجريبي</button>
            <button class="test-button" onclick="clearTestData()">مسح البيانات التجريبية</button>
            <div class="test-result" id="realtime-result">انقر على "اختبار التحديثات" للبدء...</div>
        </div>

        <!-- Homepage Integration Test -->
        <div class="test-section">
            <h3>🏠 اختبار التكامل مع الصفحة الرئيسية</h3>
            <button class="test-button" onclick="openHomepage()">فتح الصفحة الرئيسية</button>
            <button class="test-button" onclick="testHomepageUpdates()">اختبار التحديثات</button>
            <div class="test-result" id="homepage-result">انقر على "فتح الصفحة الرئيسية" للبدء...</div>
        </div>

        <!-- Performance Test -->
        <div class="test-section">
            <h3>📊 اختبار الأداء</h3>
            <button class="test-button" onclick="testPerformance()">قياس الأداء</button>
            <button class="test-button" onclick="testLoadTime()">قياس وقت التحميل</button>
            <div class="test-result" id="performance-result">انقر على "قياس الأداء" للبدء...</div>
        </div>

        <!-- System Status -->
        <div class="test-section">
            <h3>📈 حالة النظام</h3>
            <div id="system-status">
                <p>🔥 Firebase: <span class="status-indicator status-warning"></span> جاري الفحص...</p>
                <p>🛠️ لوحة الأدمن: <span class="status-indicator status-warning"></span> جاري الفحص...</p>
                <p>⚡ التحديثات اللحظية: <span class="status-indicator status-warning"></span> جاري الفحص...</p>
                <p>🏠 الصفحة الرئيسية: <span class="status-indicator status-warning"></span> جاري الفحص...</p>
            </div>
            <button class="test-button" onclick="refreshSystemStatus()">تحديث الحالة</button>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();

        // Test Functions
        async function testFirebaseConnection() {
            const result = document.getElementById('firebase-result');
            result.textContent = 'جاري فحص اتصال Firebase...\n';

            try {
                // Test connection
                const connectedRef = database.ref('.info/connected');
                const snapshot = await connectedRef.once('value');
                const connected = snapshot.val();

                result.textContent += `✅ حالة الاتصال: ${connected ? 'متصل' : 'غير متصل'}\n`;

                // Test read access
                const testRef = database.ref('siteContent');
                const testSnapshot = await testRef.once('value');
                result.textContent += `✅ صلاحية القراءة: نجحت\n`;

                // Test write access (if possible)
                try {
                    await database.ref('test').set({ timestamp: Date.now() });
                    result.textContent += `✅ صلاحية الكتابة: نجحت\n`;
                    await database.ref('test').remove();
                } catch (writeError) {
                    result.textContent += `❌ صلاحية الكتابة: فشلت - ${writeError.message}\n`;
                }

                updateSystemStatus('firebase', true);
            } catch (error) {
                result.textContent += `❌ خطأ في الاتصال: ${error.message}\n`;
                updateSystemStatus('firebase', false);
            }
        }

        async function testDatabaseRules() {
            const result = document.getElementById('firebase-result');
            result.textContent += '\nجاري فحص قواعد قاعدة البيانات...\n';

            const testPaths = ['siteContent', 'branches', 'contactSection', 'gallery', 'siteSettings'];
            
            for (const path of testPaths) {
                try {
                    const snapshot = await database.ref(path).once('value');
                    result.textContent += `✅ ${path}: يمكن القراءة\n`;
                } catch (error) {
                    result.textContent += `❌ ${path}: خطأ في القراءة - ${error.message}\n`;
                }
            }
        }

        function openAdminPanel() {
            const result = document.getElementById('admin-result');
            result.textContent = 'فتح لوحة الأدمن...\n';
            
            const adminWindow = window.open('admin.html', '_blank');
            if (adminWindow) {
                result.textContent += '✅ تم فتح لوحة الأدمن في تبويب جديد\n';
                result.textContent += 'يرجى التحقق من تحميل الصفحة بشكل صحيح\n';
                updateSystemStatus('admin', true);
            } else {
                result.textContent += '❌ فشل في فتح لوحة الأدمن (ربما تم حظر النوافذ المنبثقة)\n';
                updateSystemStatus('admin', false);
            }
        }

        async function testAdminFunctions() {
            const result = document.getElementById('admin-result');
            result.textContent += '\nاختبار وظائف لوحة الأدمن...\n';
            
            // Test if admin files exist
            const adminFiles = ['admin.html', 'admin-styles.css', 'admin-script.js'];
            
            for (const file of adminFiles) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) {
                        result.textContent += `✅ ${file}: موجود\n`;
                    } else {
                        result.textContent += `❌ ${file}: غير موجود (${response.status})\n`;
                    }
                } catch (error) {
                    result.textContent += `❌ ${file}: خطأ في الفحص\n`;
                }
            }
        }

        async function testRealtimeUpdates() {
            const result = document.getElementById('realtime-result');
            result.textContent = 'اختبار التحديثات اللحظية...\n';

            try {
                // Test listener setup
                const testRef = database.ref('test-realtime');
                
                // Set up listener
                testRef.on('value', (snapshot) => {
                    const data = snapshot.val();
                    if (data) {
                        result.textContent += `📡 تم استقبال تحديث: ${JSON.stringify(data)}\n`;
                    }
                });

                // Send test data
                await testRef.set({
                    message: 'اختبار التحديث اللحظي',
                    timestamp: Date.now()
                });

                result.textContent += '✅ تم إرسال بيانات اختبار\n';

                // Clean up
                setTimeout(async () => {
                    await testRef.remove();
                    testRef.off();
                    result.textContent += '🧹 تم تنظيف بيانات الاختبار\n';
                }, 3000);

                updateSystemStatus('realtime', true);
            } catch (error) {
                result.textContent += `❌ خطأ في اختبار التحديثات: ${error.message}\n`;
                updateSystemStatus('realtime', false);
            }
        }

        async function addTestBranch() {
            const result = document.getElementById('realtime-result');
            result.textContent += '\nإضافة فرع تجريبي...\n';

            try {
                const testBranch = {
                    name: `فرع تجريبي ${Date.now()}`,
                    address: `عنوان تجريبي - ${new Date().toLocaleString('ar-SA')}`,
                    phone: '+966501234567',
                    createdAt: new Date().toISOString()
                };

                const branchId = `test_branch_${Date.now()}`;
                await database.ref(`branches/${branchId}`).set(testBranch);
                
                result.textContent += `✅ تم إضافة فرع تجريبي: ${testBranch.name}\n`;
                result.textContent += `معرف الفرع: ${branchId}\n`;
                result.textContent += 'تحقق من ظهور الفرع في الصفحة الرئيسية\n';
            } catch (error) {
                result.textContent += `❌ خطأ في إضافة الفرع: ${error.message}\n`;
            }
        }

        async function clearTestData() {
            const result = document.getElementById('realtime-result');
            result.textContent += '\nمسح البيانات التجريبية...\n';

            try {
                // Get all branches
                const branchesSnapshot = await database.ref('branches').once('value');
                const branches = branchesSnapshot.val();

                if (branches) {
                    let deletedCount = 0;
                    for (const [branchId, branch] of Object.entries(branches)) {
                        if (branchId.startsWith('test_branch_') || branch.name.includes('تجريبي')) {
                            await database.ref(`branches/${branchId}`).remove();
                            deletedCount++;
                        }
                    }
                    result.textContent += `✅ تم حذف ${deletedCount} فرع تجريبي\n`;
                } else {
                    result.textContent += 'ℹ️ لا توجد فروع تجريبية للحذف\n';
                }
            } catch (error) {
                result.textContent += `❌ خطأ في مسح البيانات: ${error.message}\n`;
            }
        }

        function openHomepage() {
            const result = document.getElementById('homepage-result');
            result.textContent = 'فتح الصفحة الرئيسية...\n';
            
            const homepageWindow = window.open('index.html', '_blank');
            if (homepageWindow) {
                result.textContent += '✅ تم فتح الصفحة الرئيسية في تبويب جديد\n';
                result.textContent += 'تحقق من تحميل جميع العناصر بشكل صحيح\n';
                updateSystemStatus('homepage', true);
            } else {
                result.textContent += '❌ فشل في فتح الصفحة الرئيسية\n';
                updateSystemStatus('homepage', false);
            }
        }

        async function testHomepageUpdates() {
            const result = document.getElementById('homepage-result');
            result.textContent += '\nاختبار تحديثات الصفحة الرئيسية...\n';

            try {
                // Test updating company info
                const testData = {
                    title: `AL-SALAMAT - اختبار ${Date.now()}`,
                    subtitle: `رائدة في زجاج السيارات - ${new Date().toLocaleTimeString('ar-SA')}`,
                    description: `وصف تجريبي محدث في ${new Date().toLocaleString('ar-SA')}`
                };

                await database.ref('siteContent').set(testData);
                result.textContent += '✅ تم تحديث معلومات الشركة\n';
                result.textContent += 'تحقق من ظهور التحديث في الصفحة الرئيسية\n';
            } catch (error) {
                result.textContent += `❌ خطأ في اختبار التحديثات: ${error.message}\n`;
            }
        }

        async function testPerformance() {
            const result = document.getElementById('performance-result');
            result.textContent = 'قياس أداء النظام...\n';

            const startTime = performance.now();

            try {
                // Test database read performance
                const readStart = performance.now();
                await database.ref('siteContent').once('value');
                const readTime = performance.now() - readStart;
                result.textContent += `📖 وقت القراءة: ${readTime.toFixed(2)} مللي ثانية\n`;

                // Test database write performance
                const writeStart = performance.now();
                await database.ref('test-performance').set({ timestamp: Date.now() });
                const writeTime = performance.now() - writeStart;
                result.textContent += `✏️ وقت الكتابة: ${writeTime.toFixed(2)} مللي ثانية\n`;

                // Clean up
                await database.ref('test-performance').remove();

                const totalTime = performance.now() - startTime;
                result.textContent += `⏱️ الوقت الإجمالي: ${totalTime.toFixed(2)} مللي ثانية\n`;

                // Memory usage (if available)
                if (performance.memory) {
                    const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
                    result.textContent += `💾 استهلاك الذاكرة: ${memoryMB} MB\n`;
                }

            } catch (error) {
                result.textContent += `❌ خطأ في قياس الأداء: ${error.message}\n`;
            }
        }

        function testLoadTime() {
            const result = document.getElementById('performance-result');
            const loadTime = performance.now();
            result.textContent += `🚀 وقت تحميل الصفحة: ${loadTime.toFixed(2)} مللي ثانية\n`;
        }

        function updateSystemStatus(component, status) {
            const statusMap = {
                'firebase': '🔥 Firebase',
                'admin': '🛠️ لوحة الأدمن',
                'realtime': '⚡ التحديثات اللحظية',
                'homepage': '🏠 الصفحة الرئيسية'
            };

            const statusElement = document.querySelector(`#system-status p:contains("${statusMap[component]}")`);
            if (statusElement) {
                const indicator = statusElement.querySelector('.status-indicator');
                indicator.className = `status-indicator ${status ? 'status-success' : 'status-error'}`;
            }
        }

        function refreshSystemStatus() {
            // Reset all indicators to warning
            document.querySelectorAll('#system-status .status-indicator').forEach(indicator => {
                indicator.className = 'status-indicator status-warning';
            });

            // Run all tests
            testFirebaseConnection();
            setTimeout(() => {
                testAdminFunctions();
                testRealtimeUpdates();
            }, 1000);
        }

        // Auto-run initial tests
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(refreshSystemStatus, 1000);
        });
    </script>
</body>
</html>
