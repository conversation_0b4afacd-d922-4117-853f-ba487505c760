# إعداد Firebase لمشروع AL-SALAMAT

## المشكلة الحالية
تظهر رسالة خطأ: `permission_denied at /users: Client doesn't have permission to access the desired data`

هذا يعني أن قواعد الأمان في Firebase Realtime Database تمنع الوصول للبيانات.

## الحل: تحديث قواعد الأمان

### الخطوة 1: الدخول إلى Firebase Console
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروع `al-salamat`
3. من القائمة الجانبية، اختر **Realtime Database**
4. اضغط على تبويب **Rules**

### الخطوة 2: تحديث القواعد
استبدل القواعد الموجودة بالقواعد التالية:

```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null",
    
    "users": {
      ".read": "auth != null",
      ".write": "auth != null",
      "$userId": {
        ".read": "auth != null",
        ".write": "auth != null || auth.uid == $userId"
      }
    },
    
    "contactForms": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "loginActivity": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "branches": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "gallery": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "siteContent": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "siteSettings": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "serviceRequests": {
      ".read": "auth != null",
      ".write": "auth != null"
    },
    
    "test": {
      ".read": "auth != null",
      ".write": "auth != null"
    }
  }
}
```

### الخطوة 3: نشر القواعد
1. اضغط على **Publish** لحفظ القواعد الجديدة
2. انتظر بضع ثوانٍ حتى يتم تطبيق القواعد

## قواعد أمان مؤقتة للاختبار (غير آمنة)
إذا كنت تريد اختبار سريع، يمكنك استخدام هذه القواعد مؤقتاً:

```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```

**تحذير:** هذه القواعد تسمح للجميع بالقراءة والكتابة. استخدمها للاختبار فقط!

## التحقق من إعداد Authentication
1. في Firebase Console، اذهب إلى **Authentication**
2. تأكد من تفعيل **Email/Password** في تبويب **Sign-in method**
3. تأكد من وجود المستخدم `<EMAIL>` في قائمة المستخدمين

## اختبار الإعداد
1. افتح `test-firebase.html` في المتصفح
2. اضغط على "اختبار الاتصال"
3. يجب أن تظهر رسائل نجاح خضراء

## استكشاف الأخطاء
إذا استمرت المشاكل:

1. **تحقق من Console في المتصفح:**
   - اضغط F12
   - ابحث عن رسائل خطأ في Console

2. **تحقق من حالة المصادقة:**
   - تأكد من تسجيل الدخول بحساب صحيح
   - تحقق من localStorage للتأكد من وجود بيانات المستخدم

3. **تحقق من إعدادات Firebase:**
   - تأكد من صحة معرف المشروع
   - تأكد من تفعيل Realtime Database

## ملاحظات مهمة
- القواعد الجديدة تتطلب مصادقة للوصول للبيانات
- المستخدم يجب أن يكون مسجل دخول في Firebase Auth
- تم تحديث كود الإدارة للتعامل مع المصادقة بشكل صحيح

## الدعم
إذا واجهت مشاكل، تحقق من:
1. إعدادات Firebase في Console
2. رسائل الخطأ في المتصفح
3. حالة الاتصال بالإنترنت
