// Branches Display Fix - Simple and Reliable Solution
// This script ensures branches are displayed correctly on the homepage

(function() {
    'use strict';
    
    console.log('🔧 Loading Branches Display Fix...');
    
    let branchesManager = null;
    let retryCount = 0;
    const maxRetries = 5;
    
    // Simple branches manager
    class SimpleBranchesManager {
        constructor() {
            this.database = null;
            this.isInitialized = false;
            this.init();
        }
        
        async init() {
            try {
                // Wait for Firebase
                if (typeof firebase === 'undefined') {
                    console.log('⏳ Waiting for Firebase...');
                    setTimeout(() => this.init(), 1000);
                    return;
                }
                
                this.database = firebase.database();
                this.isInitialized = true;
                console.log('✅ Simple Branches Manager initialized');
                
                // Load branches immediately
                await this.loadAndDisplayBranches();
                
                // Set up real-time listener
                this.setupListener();
                
            } catch (error) {
                console.error('❌ Error initializing Simple Branches Manager:', error);
                if (retryCount < maxRetries) {
                    retryCount++;
                    console.log(`🔄 Retrying... (${retryCount}/${maxRetries})`);
                    setTimeout(() => this.init(), 2000);
                }
            }
        }
        
        setupListener() {
            try {
                const branchesRef = this.database.ref('branches');
                branchesRef.on('value', (snapshot) => {
                    const data = snapshot.val();
                    console.log('🔥 Branches data received:', data);
                    this.displayBranches(data);
                }, (error) => {
                    console.error('❌ Error in branches listener:', error);
                });
                
                console.log('👂 Branches listener set up');
            } catch (error) {
                console.error('❌ Error setting up listener:', error);
            }
        }
        
        async loadAndDisplayBranches() {
            try {
                console.log('📥 Loading branches...');
                const snapshot = await this.database.ref('branches').once('value');
                const data = snapshot.val();
                console.log('📊 Branches loaded:', data);
                this.displayBranches(data);
            } catch (error) {
                console.error('❌ Error loading branches:', error);
            }
        }
        
        displayBranches(branchesData) {
            try {
                console.log('🏢 Displaying branches...');
                
                const branchesGrid = document.getElementById('dynamic-branches');
                const noDataMessage = document.getElementById('no-branches-message');
                
                if (!branchesGrid) {
                    console.error('❌ Branches grid not found!');
                    return;
                }
                
                // Clear existing branch cards
                const existingCards = branchesGrid.querySelectorAll('.branch-card');
                existingCards.forEach(card => card.remove());
                
                if (branchesData && Object.keys(branchesData).length > 0) {
                    console.log(`✅ Displaying ${Object.keys(branchesData).length} branches`);
                    
                    // Hide no data message
                    if (noDataMessage) {
                        noDataMessage.style.display = 'none';
                        noDataMessage.classList.add('hidden');
                    }
                    
                    // Add branches
                    Object.entries(branchesData).forEach(([branchId, branch], index) => {
                        const branchCard = this.createBranchCard(branchId, branch);
                        branchesGrid.appendChild(branchCard);
                        console.log(`➕ Added branch: ${branch.name}`);
                    });
                    
                    // Add animation
                    this.animateBranches(branchesGrid);
                    
                } else {
                    console.log('⚠️ No branches data, hiding message');

                    // Hide no data message
                    if (noDataMessage) {
                        noDataMessage.style.display = 'none';
                        noDataMessage.classList.add('hidden');
                    }
                }
                
                // Verify display
                const finalCards = branchesGrid.querySelectorAll('.branch-card');
                console.log(`✅ Final result: ${finalCards.length} branch cards displayed`);
                
            } catch (error) {
                console.error('❌ Error displaying branches:', error);
            }
        }
        
        createBranchCard(branchId, branch) {
            const branchCard = document.createElement('div');
            branchCard.className = 'branch-card';
            branchCard.setAttribute('data-branch-id', branchId);
            
            const branchName = this.escapeHtml(branch.name || 'فرع غير محدد');
            const branchAddress = this.escapeHtml(branch.address || 'عنوان غير محدد');
            
            branchCard.innerHTML = `
                <h3>${branchName}</h3>
                <p>${branchAddress}</p>
                <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || '')}" target="_blank" class="location-btn">الموقع</a>
            `;
            
            return branchCard;
        }
        
        animateBranches(branchesGrid) {
            try {
                branchesGrid.style.transition = 'all 0.3s ease';
                branchesGrid.style.transform = 'scale(1.01)';
                
                setTimeout(() => {
                    branchesGrid.style.transform = 'scale(1)';
                }, 300);
            } catch (error) {
                console.error('❌ Error animating branches:', error);
            }
        }
        
        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Manual refresh method
        async refresh() {
            console.log('🔄 Manual refresh requested');
            if (this.isInitialized) {
                await this.loadAndDisplayBranches();
            } else {
                console.log('⚠️ Manager not initialized yet');
            }
        }
    }
    
    // Initialize when DOM is ready
    function initializeBranchesManager() {
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(startManager, 1000);
            });
        } else {
            setTimeout(startManager, 1000);
        }
    }
    
    function startManager() {
        console.log('🚀 Starting Simple Branches Manager...');
        branchesManager = new SimpleBranchesManager();
        
        // Make it globally accessible for debugging
        window.simpleBranchesManager = branchesManager;
        
        // Set up periodic check
        setInterval(() => {
            const branchesGrid = document.getElementById('dynamic-branches');
            if (branchesGrid) {
                const branchCards = branchesGrid.querySelectorAll('.branch-card');
                if (branchCards.length === 0 && branchesManager && branchesManager.isInitialized) {
                    console.log('🔄 No branches visible, refreshing...');
                    branchesManager.refresh();
                }
            }
        }, 10000); // Check every 10 seconds
    }
    
    // Start initialization
    initializeBranchesManager();
    
    // Export for manual use
    window.branchesDisplayFix = {
        refresh: () => branchesManager && branchesManager.refresh(),
        getManager: () => branchesManager,
        isReady: () => branchesManager && branchesManager.isInitialized
    };
    
    console.log('✅ Branches Display Fix loaded');
    
})();
