# حل سريع لمشكلة permission_denied

## المشكلة
```
خطأ في تحميل بيانات لوحة التحكم: permission_denied at /users: Client doesn't have permission to access the desired data.
```

## الحل السريع (3 خطوات)

### الخطوة 1: تحديث قواعد Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروع `al-salamat`
3. اذهب إلى **Realtime Database** > **Rules**
4. استبدل القواعد بهذا الكود:

```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}
```

5. اضغط **Publish**

### الخطوة 2: إنشاء حساب المدير
1. اذهب إلى `login.html`
2. اختر "إنشاء حساب"
3. أدخل البيانات:
   - **الاسم**: admin
   - **البريد الإلكتروني**: <EMAIL>
   - **رقم الهاتف**: أي رقم
   - **كلمة المرور**: اختر كلمة مرور قوية
4. اضغط "إنشاء حساب"

### الخطوة 3: اختبار لوحة الإدارة
1. بعد إنشاء الحساب، ستكون مسجل دخول تلقائياً
2. اذهب إلى `admin.html`
3. يجب أن تعمل لوحة الإدارة الآن بدون أخطاء

## اختبار إضافي
افتح `test-firebase.html` للتأكد من:
- ✅ اتصال Firebase
- ✅ تسجيل الدخول
- ✅ قراءة وكتابة البيانات

## إذا استمرت المشكلة

### حل مؤقت (للاختبار فقط)
استخدم هذه القواعد في Firebase:
```json
{
  "rules": {
    ".read": true,
    ".write": true
  }
}
```

**تحذير**: هذه القواعد غير آمنة! استخدمها للاختبار فقط.

### تحقق من:
1. **تسجيل الدخول**: تأكد من تسجيل الدخول بحساب صحيح
2. **الإنترنت**: تأكد من اتصال الإنترنت
3. **المتصفح**: جرب متصفح آخر أو وضع التصفح الخفي
4. **Console**: اضغط F12 وتحقق من رسائل الخطأ

## ملاحظات
- القواعد الجديدة تتطلب تسجيل دخول للوصول للبيانات
- حساب `<EMAIL>` يحصل على صلاحيات إدارية تلقائياً
- تم تحديث كود الإدارة للتعامل مع المصادقة

## الدعم
إذا واجهت مشاكل، تحقق من ملف `FIREBASE-SETUP.md` للتفاصيل الكاملة.
