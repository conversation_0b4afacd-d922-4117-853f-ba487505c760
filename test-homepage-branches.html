<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الفروع في الصفحة الرئيسية - AL-SALAMAT</title>
    <link rel="stylesheet" href="styles.css">
    
    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <!-- Dynamic Content Manager -->
    <script src="dynamic-content.js"></script>

    <!-- Emergency Fix Script -->
    <script src="fix-branches.js"></script>
    
    <style>
        body {
            padding: 20px;
            background: #f5f7fa;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        .test-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        
        .console-output {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار عرض الفروع في الصفحة الرئيسية</h1>
            <p>هذه الصفحة تحاكي نفس البيئة الموجودة في index.html</p>
        </div>
        
        <div class="test-controls">
            <button class="btn" onclick="testConnection()">اختبار الاتصال</button>
            <button class="btn" onclick="forceReload()">إعادة تحميل الفروع</button>
            <button class="btn" onclick="checkDOMElements()">فحص العناصر</button>
            <button class="btn" onclick="window.branchesFix.fixBranches()" style="background: #28a745;">🔧 إصلاح الفروع</button>
            <button class="btn" onclick="clearConsole()">مسح الكونسول</button>
        </div>
        
        <div id="status-area"></div>
        
        <div class="console-output" id="console-output"></div>
        
        <!-- هذا هو نفس قسم الفروع الموجود في index.html -->
        <section id="branches" class="branches-section">
            <h2 class="branches-title">فروعنا</h2>
            <div class="branches-grid" id="dynamic-branches">
                <!-- سيتم تحميل الفروع من Firebase -->
                <div class="no-data-message" id="no-branches-message">
                    <p>لا توجد فروع مضافة حالياً. يرجى إضافة الفروع من لوحة الإدارة.</p>
                </div>
            </div>
        </section>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        console.log('Firebase initialized successfully');

        // Custom console logging
        const originalLog = console.log;
        const originalError = console.error;
        const consoleOutput = document.getElementById('console-output');

        function logToConsole(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.textContent += logEntry;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            logToConsole(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logToConsole(args.join(' '), 'error');
        };

        function showStatus(message, type) {
            const statusArea = document.getElementById('status-area');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusArea.appendChild(statusDiv);
            
            // Remove after 5 seconds
            setTimeout(() => {
                statusDiv.remove();
            }, 5000);
        }

        function clearConsole() {
            consoleOutput.textContent = '';
        }

        async function testConnection() {
            try {
                showStatus('جاري اختبار الاتصال...', 'info');
                const database = firebase.database();
                const testRef = database.ref('.info/connected');
                const snapshot = await testRef.once('value');
                
                if (snapshot.val() === true) {
                    showStatus('✅ الاتصال بـ Firebase ناجح!', 'success');
                    console.log('✅ Firebase connection successful');
                } else {
                    showStatus('❌ فشل في الاتصال بـ Firebase', 'error');
                    console.error('❌ Firebase connection failed');
                }
            } catch (error) {
                showStatus(`❌ خطأ في الاتصال: ${error.message}`, 'error');
                console.error('❌ Connection error:', error);
            }
        }

        async function forceReload() {
            try {
                showStatus('جاري إعادة تحميل الفروع...', 'info');
                console.log('🔄 Force reloading branches...');
                
                if (window.dynamicContentManager) {
                    await window.dynamicContentManager.loadBranches();
                    showStatus('✅ تم إعادة تحميل الفروع', 'success');
                } else {
                    showStatus('❌ Dynamic Content Manager غير موجود', 'error');
                    console.error('❌ Dynamic Content Manager not found');
                }
            } catch (error) {
                showStatus(`❌ خطأ في إعادة التحميل: ${error.message}`, 'error');
                console.error('❌ Reload error:', error);
            }
        }

        function checkDOMElements() {
            console.log('🔍 Checking DOM elements...');
            
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');
            const branchCards = branchesGrid ? branchesGrid.querySelectorAll('.branch-card') : [];
            
            console.log('📍 Branches grid:', branchesGrid ? 'Found' : 'Not found');
            console.log('📝 No data message:', noDataMessage ? 'Found' : 'Not found');
            console.log('🏢 Branch cards count:', branchCards.length);
            
            if (noDataMessage) {
                console.log('👁️ No data message visibility:', noDataMessage.style.display);
                console.log('🙈 No data message classes:', noDataMessage.className);
            }
            
            showStatus(`DOM: Grid=${branchesGrid ? '✅' : '❌'}, Message=${noDataMessage ? '✅' : '❌'}, Cards=${branchCards.length}`, 'info');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 Test page loaded');
            showStatus('صفحة الاختبار تم تحميلها', 'info');
            
            // Auto-run tests
            setTimeout(() => {
                testConnection();
                setTimeout(() => {
                    checkDOMElements();
                }, 2000);
            }, 1000);
        });

        // Monitor Dynamic Content Manager
        let checkManagerInterval = setInterval(() => {
            if (window.dynamicContentManager) {
                console.log('✅ Dynamic Content Manager is ready');
                showStatus('✅ Dynamic Content Manager جاهز', 'success');
                clearInterval(checkManagerInterval);
                
                // Force load branches after manager is ready
                setTimeout(() => {
                    forceReload();
                }, 1000);
            } else {
                console.log('⏳ Waiting for Dynamic Content Manager...');
            }
        }, 1000);

        // Stop checking after 10 seconds
        setTimeout(() => {
            clearInterval(checkManagerInterval);
            if (!window.dynamicContentManager) {
                showStatus('❌ Dynamic Content Manager لم يتم تحميله', 'error');
                console.error('❌ Dynamic Content Manager failed to load');
            }
        }, 10000);
    </script>
</body>
</html>
