// Enhanced Real-time Updates for AL-SALAMAT Website
// This file provides improved real-time synchronization with Firebase

class EnhancedRealtimeManager {
    constructor() {
        this.database = null;
        this.listeners = [];
        this.lastUpdateTime = {};
        this.isOnline = navigator.onLine;
        this.retryCount = 0;
        this.maxRetries = 5;
        this.updateQueue = [];
        this.isProcessingQueue = false;
        this.init();
    }

    async init() {
        try {
            console.log('🚀 Initializing Enhanced Realtime Manager...');

            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                console.log('⏳ Waiting for Firebase...');
                this.retryCount++;
                if (this.retryCount < this.maxRetries) {
                    setTimeout(() => this.init(), 1000);
                } else {
                    console.error('❌ Firebase failed to load after maximum retries');
                }
                return;
            }

            // Wait for authentication to be ready
            if (!window.homepageAuth || !window.homepageAuth.isUserAuthenticated()) {
                console.log('🔐 Waiting for homepage authentication...');
                setTimeout(() => this.init(), 2000);
                return;
            }

            this.database = firebase.database();

            // Enable real-time sync and offline persistence
            this.database.goOnline();

            console.log('✅ Enhanced Realtime Manager initialized with authentication');

            // Set up network monitoring
            this.setupNetworkMonitoring();

            // Set up real-time listeners with enhanced error handling
            this.setupEnhancedListeners();

            // Set up periodic sync check
            this.setupPeriodicSync();

            // Process any queued updates
            this.processUpdateQueue();

        } catch (error) {
            console.error('❌ Error initializing Enhanced Realtime Manager:', error);
            setTimeout(() => this.init(), 2000);
        }
    }

    // Set up network monitoring
    setupNetworkMonitoring() {
        window.addEventListener('online', () => {
            console.log('🌐 Network restored - syncing data...');
            this.isOnline = true;
            this.database.goOnline();
            this.forceSync();
        });

        window.addEventListener('offline', () => {
            console.log('📴 Network lost - enabling offline mode...');
            this.isOnline = false;
        });
    }

    // Set up enhanced real-time listeners
    setupEnhancedListeners() {
        console.log('🎧 Setting up enhanced real-time listeners...');

        // Company info listener
        this.setupListener('siteContent', (data) => {
            if (data) {
                this.updateCompanyInfo(data);
                this.showNotification('تم تحديث معلومات الشركة');
            }
        });

        // Contact section listener
        this.setupListener('contactSection', (data) => {
            if (data) {
                this.updateContactSection(data);
                this.showNotification('تم تحديث معلومات التواصل');
            }
        });

        // Branches listener
        this.setupListener('branches', (data) => {
            this.updateBranches(data);
            this.showNotification('تم تحديث الفروع');
        });

        // Gallery listener
        this.setupListener('gallery', (data) => {
            this.updateGallery(data);
            this.showNotification('تم تحديث المعرض');
        });

        // Site settings listener
        this.setupListener('siteSettings', (data) => {
            if (data) {
                this.updateSiteSettings(data);
                this.showNotification('تم تحديث إعدادات الموقع');
            }
        });

        console.log('✅ All enhanced listeners set up');
    }

    // Generic listener setup with error handling and retry logic
    setupListener(path, updateCallback) {
        const ref = this.database.ref(path);
        const listener = ref.on('value', (snapshot) => {
            try {
                const data = snapshot.val();
                const timestamp = Date.now();
                
                // Prevent duplicate processing
                if (!this.lastUpdateTime[path] || timestamp - this.lastUpdateTime[path] > 300) {
                    this.lastUpdateTime[path] = timestamp;
                    console.log(`🔄 ${path} updated:`, data);
                    
                    // Add to update queue for processing
                    this.addToUpdateQueue(path, data, updateCallback);
                }
            } catch (error) {
                console.error(`❌ Error in ${path} listener:`, error);
            }
        }, (error) => {
            console.error(`❌ ${path} listener error:`, error);
            this.retryListener(path, updateCallback);
        });

        this.listeners.push({ path, ref, listener });
    }

    // Add update to queue for processing
    addToUpdateQueue(path, data, callback) {
        this.updateQueue.push({ path, data, callback, timestamp: Date.now() });
        this.processUpdateQueue();
    }

    // Process update queue
    async processUpdateQueue() {
        if (this.isProcessingQueue || this.updateQueue.length === 0) return;
        
        this.isProcessingQueue = true;
        
        while (this.updateQueue.length > 0) {
            const update = this.updateQueue.shift();
            try {
                await update.callback(update.data);
                console.log(`✅ Processed update for ${update.path}`);
            } catch (error) {
                console.error(`❌ Error processing update for ${update.path}:`, error);
            }
        }
        
        this.isProcessingQueue = false;
    }

    // Retry failed listeners
    retryListener(path, updateCallback) {
        console.log(`🔄 Retrying listener for ${path}...`);
        setTimeout(() => {
            if (this.isOnline) {
                this.setupListener(path, updateCallback);
            }
        }, 5000);
    }

    // Force sync all data
    async forceSync() {
        try {
            console.log('🔄 Force syncing all data...');
            
            // Temporarily go offline and online to clear cache
            this.database.goOffline();
            await new Promise(resolve => setTimeout(resolve, 100));
            this.database.goOnline();
            
            console.log('✅ Force sync completed');
        } catch (error) {
            console.error('❌ Error during force sync:', error);
        }
    }

    // Set up periodic sync check
    setupPeriodicSync() {
        setInterval(() => {
            if (this.isOnline) {
                console.log('🔍 Periodic sync check...');
                this.forceSync();
            }
        }, 60000); // Every minute
    }

    // Update company info
    updateCompanyInfo(data) {
        try {
            if (data && data.title) {
                const titleElement = document.getElementById('company-title');
                if (titleElement && titleElement.textContent !== data.title) {
                    titleElement.textContent = data.title;
                    this.animateElement(titleElement);
                }
            }

            if (data && data.subtitle) {
                const subtitleElement = document.getElementById('company-subtitle');
                if (subtitleElement && subtitleElement.textContent !== data.subtitle) {
                    subtitleElement.textContent = data.subtitle;
                    this.animateElement(subtitleElement);
                }
            }

            if (data && data.description) {
                const descriptionElement = document.getElementById('company-description');
                if (descriptionElement && descriptionElement.textContent !== data.description) {
                    descriptionElement.textContent = data.description;
                    this.animateElement(descriptionElement);
                }
            }

            console.log('✅ Company info updated');
        } catch (error) {
            console.error('❌ Error updating company info:', error);
        }
    }

    // Update contact section
    updateContactSection(data) {
        try {
            const updates = [
                { id: 'contact-title', value: data.title },
                { id: 'contact-info-title', value: data.infoTitle },
                { id: 'contact-address-display', value: data.address },
                { id: 'contact-hours-display', value: data.hours }
            ];

            updates.forEach(update => {
                const element = document.getElementById(update.id);
                if (element && update.value && element.textContent !== update.value) {
                    element.textContent = update.value;
                    this.animateElement(element);
                }
            });

            console.log('✅ Contact section updated');
        } catch (error) {
            console.error('❌ Error updating contact section:', error);
        }
    }

    // Update branches with immediate rendering
    updateBranches(branchesData) {
        try {
            console.log('🏢 Updating branches immediately...');
            const branchesGrid = document.getElementById('dynamic-branches');
            const noDataMessage = document.getElementById('no-branches-message');

            if (!branchesGrid) {
                console.error('❌ Branches grid not found!');
                return;
            }

            // Clear existing branches
            const existingCards = branchesGrid.querySelectorAll('.branch-card');
            existingCards.forEach(card => card.remove());

            if (branchesData && Object.keys(branchesData).length > 0) {
                // Hide no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'none';
                }

                // Add new branches
                Object.entries(branchesData).forEach(([branchId, branch]) => {
                    const branchCard = document.createElement('div');
                    branchCard.className = 'branch-card';
                    branchCard.innerHTML = `
                        <h3>${this.escapeHtml(branch.name || 'فرع غير محدد')}</h3>
                        <p>${this.escapeHtml(branch.address || 'عنوان غير محدد')}</p>
                        <a href="https://maps.google.com/?q=${encodeURIComponent(branch.address || '')}" target="_blank" class="location-btn">الموقع</a>
                    `;
                    branchesGrid.appendChild(branchCard);
                });

                this.animateElement(branchesGrid);
            } else {
                // Show no data message
                if (noDataMessage) {
                    noDataMessage.style.display = 'block';
                }
            }

            console.log('✅ Branches updated successfully');
        } catch (error) {
            console.error('❌ Error updating branches:', error);
        }
    }

    // Update gallery
    updateGallery(galleryData) {
        try {
            const galleryGrid = document.getElementById('dynamic-gallery');
            if (!galleryGrid) return;

            // Remove Firebase images only
            const firebaseImages = galleryGrid.querySelectorAll('.firebase-image');
            firebaseImages.forEach(img => img.remove());

            if (galleryData && Object.keys(galleryData).length > 0) {
                Object.values(galleryData).forEach(image => {
                    const galleryItem = document.createElement('div');
                    galleryItem.className = 'gallery-item firebase-image';
                    galleryItem.innerHTML = `
                        <img src="${this.escapeHtml(image.url)}" 
                             alt="${this.escapeHtml(image.alt || 'صورة من المعرض')}" 
                             class="gallery-image" loading="lazy">
                    `;
                    galleryGrid.appendChild(galleryItem);
                });

                this.animateElement(galleryGrid);
            }

            console.log('✅ Gallery updated');
        } catch (error) {
            console.error('❌ Error updating gallery:', error);
        }
    }

    // Update site settings
    updateSiteSettings(settings) {
        try {
            if (settings.contactEmail) {
                const emailElement = document.getElementById('contact-email-display');
                if (emailElement && emailElement.textContent !== settings.contactEmail) {
                    emailElement.textContent = settings.contactEmail;
                    this.animateElement(emailElement);
                }
            }

            if (settings.contactPhone) {
                const phoneElement = document.getElementById('contact-phone-display');
                if (phoneElement && phoneElement.textContent !== settings.contactPhone) {
                    phoneElement.textContent = settings.contactPhone;
                    this.animateElement(phoneElement);
                }
            }

            console.log('✅ Site settings updated');
        } catch (error) {
            console.error('❌ Error updating site settings:', error);
        }
    }

    // Animate element update
    animateElement(element) {
        if (!element) return;

        element.style.transition = 'all 0.3s ease';
        element.style.transform = 'scale(1.02)';
        element.style.boxShadow = '0 0 15px rgba(76, 175, 80, 0.4)';

        setTimeout(() => {
            element.style.transform = 'scale(1)';
            element.style.boxShadow = '';
        }, 300);
    }

    // Show update notification (disabled)
    showNotification(message) {
        // Don't show notifications
        console.log(`🔔 ${message} (notification disabled)`);
    }

    // Escape HTML
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Clean up
    destroy() {
        this.listeners.forEach(({ ref, listener }) => {
            ref.off('value', listener);
        });
        this.listeners = [];
        console.log('🧹 Enhanced Realtime Manager cleaned up');
    }
}

// Initialize when ready
function initializeEnhancedRealtime() {
    if (typeof firebase !== 'undefined') {
        window.enhancedRealtimeManager = new EnhancedRealtimeManager();
        console.log('✅ Enhanced Realtime Manager started');
    } else {
        setTimeout(initializeEnhancedRealtime, 1000);
    }
}

// Auto-initialize
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeEnhancedRealtime, 1000);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.enhancedRealtimeManager) {
        window.enhancedRealtimeManager.destroy();
    }
});
