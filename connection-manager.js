// Connection Manager for AL-SALAMAT Real-time Updates
// Manages connection status, sync indicators, and cache clearing

class ConnectionManager {
    constructor() {
        this.isOnline = navigator.onLine;
        this.isConnectedToFirebase = false;
        this.lastSyncTime = null;
        this.syncInterval = null;
        this.retryAttempts = 0;
        this.maxRetryAttempts = 5;
        this.init();
    }

    init() {
        console.log('🔌 Initializing Connection Manager...');
        
        // Set up network event listeners
        this.setupNetworkListeners();
        
        // Set up Firebase connection monitoring
        this.setupFirebaseConnectionMonitoring();
        
        // Set up periodic sync check
        this.setupPeriodicSync();
        
        // Update initial status
        this.updateConnectionStatus();
        
        console.log('✅ Connection Manager initialized');
    }

    setupNetworkListeners() {
        window.addEventListener('online', () => {
            console.log('🌐 Network connection restored');
            this.isOnline = true;
            this.retryAttempts = 0;
            this.updateConnectionStatus();
            this.attemptFirebaseReconnection();
        });

        window.addEventListener('offline', () => {
            console.log('📴 Network connection lost');
            this.isOnline = false;
            this.isConnectedToFirebase = false;
            this.updateConnectionStatus();
        });
    }

    setupFirebaseConnectionMonitoring() {
        // Wait for Firebase and authentication to be available
        const checkFirebase = () => {
            if (typeof firebase !== 'undefined' && firebase.database &&
                window.homepageAuth && window.homepageAuth.isUserAuthenticated()) {

                const connectedRef = firebase.database().ref('.info/connected');
                connectedRef.on('value', (snapshot) => {
                    const connected = snapshot.val();
                    this.isConnectedToFirebase = connected;
                    this.updateConnectionStatus();

                    if (connected) {
                        console.log('🔥 Connected to Firebase with authentication');
                        this.lastSyncTime = new Date();
                        this.retryAttempts = 0;
                        this.clearCache();
                    } else {
                        console.log('🔥 Disconnected from Firebase');
                        this.scheduleReconnection();
                    }
                });
            } else {
                setTimeout(checkFirebase, 1000);
            }
        };

        checkFirebase();
    }

    setupPeriodicSync() {
        // Check sync status every 30 seconds
        this.syncInterval = setInterval(() => {
            this.checkSyncStatus();
        }, 30000);
    }

    checkSyncStatus() {
        if (this.isOnline && this.isConnectedToFirebase) {
            const now = new Date();
            const timeSinceLastSync = this.lastSyncTime ? now - this.lastSyncTime : Infinity;
            
            // If more than 2 minutes since last sync, force refresh
            if (timeSinceLastSync > 120000) {
                console.log('⏰ Forcing sync due to timeout');
                this.forceSyncAll();
            }
        }
    }

    attemptFirebaseReconnection() {
        if (!this.isConnectedToFirebase && this.retryAttempts < this.maxRetryAttempts) {
            this.retryAttempts++;
            console.log(`🔄 Attempting Firebase reconnection (${this.retryAttempts}/${this.maxRetryAttempts})`);
            
            this.showSyncStatus('reconnecting');
            
            setTimeout(() => {
                if (typeof firebase !== 'undefined' && firebase.database) {
                    firebase.database().goOffline();
                    setTimeout(() => {
                        firebase.database().goOnline();
                    }, 1000);
                }
            }, 2000);
        }
    }

    scheduleReconnection() {
        if (this.isOnline && this.retryAttempts < this.maxRetryAttempts) {
            setTimeout(() => {
                this.attemptFirebaseReconnection();
            }, 5000 * Math.pow(2, this.retryAttempts)); // Exponential backoff
        }
    }

    forceSyncAll() {
        this.showProgressBar();
        
        // Clear browser cache
        this.clearCache();
        
        // Force refresh all managers
        if (window.enhancedRealtimeManager) {
            window.enhancedRealtimeManager.forceSync();
        }
        
        if (window.dynamicContentManager) {
            window.dynamicContentManager.forceRefreshAllContent();
        }
        
        setTimeout(() => {
            this.hideProgressBar();
        }, 2000);
    }

    clearCache() {
        try {
            // Clear localStorage cache
            const cacheKeys = Object.keys(localStorage).filter(key => 
                key.startsWith('firebase_') || key.startsWith('al-salamat_')
            );
            cacheKeys.forEach(key => localStorage.removeItem(key));
            
            // Clear sessionStorage cache
            const sessionKeys = Object.keys(sessionStorage).filter(key => 
                key.startsWith('firebase_') || key.startsWith('al-salamat_')
            );
            sessionKeys.forEach(key => sessionStorage.removeItem(key));
            
            console.log('🧹 Cache cleared');
        } catch (error) {
            console.error('❌ Error clearing cache:', error);
        }
    }

    updateConnectionStatus() {
        const statusElement = document.getElementById('connection-status');
        
        if (!statusElement) return;
        
        if (this.isOnline && this.isConnectedToFirebase) {
            statusElement.className = 'connection-status connected show';
            statusElement.textContent = 'متصل - التحديثات اللحظية نشطة';
            this.showSyncStatus('online');
            
            // Hide status after 3 seconds if connected
            setTimeout(() => {
                statusElement.classList.remove('show');
            }, 3000);
        } else if (this.isOnline && !this.isConnectedToFirebase) {
            statusElement.className = 'connection-status reconnecting show';
            statusElement.textContent = 'جاري الاتصال بالخادم...';
            this.showSyncStatus('syncing');
        } else {
            statusElement.className = 'connection-status disconnected show';
            statusElement.textContent = 'غير متصل - سيتم التحديث عند استعادة الاتصال';
            this.showSyncStatus('offline');
        }
    }

    showSyncStatus(status) {
        const syncElement = document.getElementById('sync-status');
        if (!syncElement) return;
        
        switch (status) {
            case 'online':
                syncElement.className = 'sync-status online';
                syncElement.textContent = '🟢 متصل';
                break;
            case 'offline':
                syncElement.className = 'sync-status offline';
                syncElement.textContent = '🔴 غير متصل';
                break;
            case 'syncing':
                syncElement.className = 'sync-status syncing';
                syncElement.textContent = '🟡 جاري المزامنة...';
                break;
            case 'reconnecting':
                syncElement.className = 'sync-status syncing';
                syncElement.textContent = '🔄 إعادة الاتصال...';
                break;
        }
    }

    showProgressBar() {
        const progressElement = document.getElementById('update-progress');
        if (progressElement) {
            progressElement.classList.add('show');
        }
    }

    hideProgressBar() {
        const progressElement = document.getElementById('update-progress');
        if (progressElement) {
            progressElement.classList.remove('show');
        }
    }

    // Manual refresh function
    manualRefresh() {
        console.log('🔄 Manual refresh triggered');
        this.showProgressBar();
        this.forceSyncAll();
        
        // Show notification
        this.showNotification('جاري تحديث البيانات...', 'info');
        
        setTimeout(() => {
            this.showNotification('تم تحديث البيانات بنجاح', 'success');
        }, 2000);
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `realtime-notification ${type}`;
        notification.textContent = message;
        
        const colors = {
            success: 'linear-gradient(135deg, #4CAF50, #45a049)',
            error: 'linear-gradient(135deg, #f44336, #d32f2f)',
            info: 'linear-gradient(135deg, #2196F3, #1976D2)',
            warning: 'linear-gradient(135deg, #ff9800, #f57c00)'
        };
        
        notification.style.background = colors[type] || colors.info;
        document.body.appendChild(notification);
        
        setTimeout(() => notification.classList.add('show'), 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    // Get connection info
    getConnectionInfo() {
        return {
            isOnline: this.isOnline,
            isConnectedToFirebase: this.isConnectedToFirebase,
            lastSyncTime: this.lastSyncTime,
            retryAttempts: this.retryAttempts
        };
    }

    // Cleanup
    destroy() {
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
        }
        console.log('🧹 Connection Manager cleaned up');
    }
}

// Global functions for manual control
window.manualRefresh = function() {
    if (window.connectionManager) {
        window.connectionManager.manualRefresh();
    }
};

window.clearAllCache = function() {
    if (window.connectionManager) {
        window.connectionManager.clearCache();
        window.connectionManager.showNotification('تم مسح الذاكرة المؤقتة', 'success');
    }
};

window.getConnectionStatus = function() {
    if (window.connectionManager) {
        return window.connectionManager.getConnectionInfo();
    }
    return null;
};

// Initialize Connection Manager
function initializeConnectionManager() {
    if (typeof firebase !== 'undefined') {
        window.connectionManager = new ConnectionManager();
        console.log('✅ Connection Manager started');
        
        // Add keyboard shortcut for manual refresh (Ctrl+R or Cmd+R)
        document.addEventListener('keydown', (event) => {
            if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
                event.preventDefault();
                window.manualRefresh();
            }
        });
        
    } else {
        setTimeout(initializeConnectionManager, 1000);
    }
}

// Auto-initialize
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeConnectionManager, 500);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.connectionManager) {
        window.connectionManager.destroy();
    }
});
