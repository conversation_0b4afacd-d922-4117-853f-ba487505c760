// Fix Branches Display - Emergency Script
// Run this in the browser console to diagnose and fix the branches display issue

console.log('🚀 Starting branches fix script...');

// Function to check Firebase connection
async function checkFirebase() {
    try {
        if (typeof firebase === 'undefined') {
            console.error('❌ Firebase not loaded!');
            return false;
        }
        
        const database = firebase.database();
        const testRef = database.ref('.info/connected');
        const snapshot = await testRef.once('value');
        
        if (snapshot.val() === true) {
            console.log('✅ Firebase connection OK');
            return true;
        } else {
            console.error('❌ Firebase not connected');
            return false;
        }
    } catch (error) {
        console.error('❌ Firebase error:', error);
        return false;
    }
}

// Function to check DOM elements
function checkDOM() {
    console.log('🔍 Checking DOM elements...');
    
    const branchesGrid = document.getElementById('dynamic-branches');
    const noDataMessage = document.getElementById('no-branches-message');
    
    console.log('📍 Branches grid:', branchesGrid ? '✅ Found' : '❌ Not found');
    console.log('📝 No data message:', noDataMessage ? '✅ Found' : '❌ Not found');
    
    if (branchesGrid) {
        const branchCards = branchesGrid.querySelectorAll('.branch-card');
        console.log(`🏢 Branch cards: ${branchCards.length}`);
        
        branchCards.forEach((card, index) => {
            console.log(`  Card ${index + 1}:`, card.textContent.trim());
        });
    }
    
    if (noDataMessage) {
        console.log('👁️ No data message display:', noDataMessage.style.display);
        console.log('🙈 No data message classes:', noDataMessage.className);
        console.log('📄 No data message visible:', !noDataMessage.classList.contains('hidden') && noDataMessage.style.display !== 'none');
    }
    
    return { branchesGrid, noDataMessage };
}

// Function to load branches manually
async function loadBranchesManually() {
    try {
        console.log('🔄 Loading branches manually...');
        
        const database = firebase.database();
        const snapshot = await database.ref('branches').once('value');
        const branchesData = snapshot.val();
        
        console.log('📥 Branches data:', branchesData);
        console.log('📊 Number of branches:', branchesData ? Object.keys(branchesData).length : 0);
        
        return branchesData;
    } catch (error) {
        console.error('❌ Error loading branches:', error);
        return null;
    }
}

// Function to manually update branches in DOM
function updateBranchesManually(branchesData) {
    console.log('🔧 Manually updating branches in DOM...');
    
    const branchesGrid = document.getElementById('dynamic-branches');
    const noDataMessage = document.getElementById('no-branches-message');
    
    if (!branchesGrid) {
        console.error('❌ Branches grid not found!');
        return false;
    }
    
    if (branchesData && Object.keys(branchesData).length > 0) {
        console.log('✅ Found branches data, updating...');
        
        // Hide no data message
        if (noDataMessage) {
            noDataMessage.style.display = 'none';
            noDataMessage.classList.add('hidden');
            console.log('🙈 Hidden no data message');
        }
        
        // Clear existing cards
        const existingCards = branchesGrid.querySelectorAll('.branch-card');
        existingCards.forEach(card => card.remove());
        console.log(`🗑️ Removed ${existingCards.length} existing cards`);
        
        // Add new branches
        Object.entries(branchesData).forEach(([branchId, branch], index) => {
            console.log(`➕ Adding branch ${index + 1}:`, branch);
            
            const branchCard = document.createElement('div');
            branchCard.className = 'branch-card';
            branchCard.setAttribute('data-branch-id', branchId);
            
            const branchName = branch.name || 'فرع غير محدد';
            const branchAddress = branch.address || 'عنوان غير محدد';
            
            branchCard.innerHTML = `
                <h3>${escapeHtml(branchName)}</h3>
                <p>${escapeHtml(branchAddress)}</p>
                <a href="https://maps.google.com/?q=${encodeURIComponent(branchAddress)}" target="_blank" class="location-btn">الموقع</a>
            `;
            
            branchesGrid.appendChild(branchCard);
        });
        
        console.log('✅ Branches added successfully');
        
        // Verify
        const finalCards = branchesGrid.querySelectorAll('.branch-card');
        console.log(`✅ Final verification: ${finalCards.length} branch cards in DOM`);
        
        return true;
    } else {
        console.log('⚠️ No branches data found');
        
        // Show no data message
        if (noDataMessage) {
            noDataMessage.style.display = 'block';
            noDataMessage.classList.remove('hidden');
            console.log('👁️ Showed no data message');
        }
        
        // Remove existing cards
        const existingCards = branchesGrid.querySelectorAll('.branch-card');
        existingCards.forEach(card => card.remove());
        console.log(`🗑️ Removed ${existingCards.length} existing cards`);
        
        return false;
    }
}

// Escape HTML function
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to check Dynamic Content Manager
function checkDynamicManager() {
    console.log('🔍 Checking Dynamic Content Manager...');
    
    if (typeof DynamicContentManager !== 'undefined') {
        console.log('✅ DynamicContentManager class found');
        
        if (window.dynamicContentManager) {
            console.log('✅ Instance found in window.dynamicContentManager');
            return window.dynamicContentManager;
        } else {
            console.log('⚠️ No instance found, creating new one...');
            try {
                window.dynamicContentManager = new DynamicContentManager();
                console.log('✅ New instance created');
                return window.dynamicContentManager;
            } catch (error) {
                console.error('❌ Error creating instance:', error);
                return null;
            }
        }
    } else {
        console.error('❌ DynamicContentManager class not found!');
        return null;
    }
}

// Main fix function
async function fixBranches() {
    console.log('🔧 Starting branches fix...');
    
    // Step 1: Check Firebase
    const firebaseOK = await checkFirebase();
    if (!firebaseOK) {
        console.error('❌ Cannot proceed without Firebase connection');
        return false;
    }
    
    // Step 2: Check DOM
    const { branchesGrid, noDataMessage } = checkDOM();
    if (!branchesGrid) {
        console.error('❌ Cannot proceed without branches grid element');
        return false;
    }
    
    // Step 3: Load branches data
    const branchesData = await loadBranchesManually();
    
    // Step 4: Update DOM manually
    const success = updateBranchesManually(branchesData);
    
    // Step 5: Check Dynamic Content Manager
    const manager = checkDynamicManager();
    
    if (success) {
        console.log('🎉 Branches fix completed successfully!');
    } else {
        console.log('⚠️ Fix completed but no branches data found');
    }
    
    return success;
}

// Auto-run the fix
console.log('🚀 Auto-running branches fix in 2 seconds...');
setTimeout(fixBranches, 2000);

// Export functions for manual use
window.branchesFix = {
    checkFirebase,
    checkDOM,
    loadBranchesManually,
    updateBranchesManually,
    checkDynamicManager,
    fixBranches
};

console.log('✅ Branches fix script loaded. Use window.branchesFix.fixBranches() to run manually.');
