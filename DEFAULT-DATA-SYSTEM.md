# نظام البيانات الافتراضية المنظم

## نظرة عامة

تم إنشاء نظام شامل لإدارة البيانات الافتراضية للموقع مع إمكانية إعادة التعيين والتصدير والاستيراد. النظام منظم بشكل هرمي في Firebase Realtime Database.

## هيكل البيانات في Firebase

```
al-salamat-database/
├── companyInfo/
│   ├── title: "AL-SALAMAT"
│   ├── subtitle: "شركة السلامات للخدمات المتكاملة"
│   ├── description: "نحن شركة رائدة..."
│   ├── updatedAt: "2024-01-01T00:00:00.000Z"
│   └── version: "1.0"
├── aboutSection/
│   ├── title: "من نحن"
│   ├── description: "شركة السلامات هي شركة رائدة..."
│   ├── updatedAt: "2024-01-01T00:00:00.000Z"
│   └── version: "1.0"
├── contactSection/
│   ├── title: "اتصل بنا"
│   ├── infoTitle: "معلومات التواصل"
│   ├── address: "المملكة العربية السعودية، الرياض"
│   ├── hours: "السبت - الخميس: 8:00 ص - 10:00 م | الجمعة: مغلق"
│   ├── updatedAt: "2024-01-01T00:00:00.000Z"
│   └── version: "1.0"
├── siteSettings/
│   ├── maintenance: false
│   ├── contactEmail: "<EMAIL>"
│   ├── contactPhone: "+966501234567"
│   ├── updatedAt: "2024-01-01T00:00:00.000Z"
│   └── version: "1.0"
├── branches/ (يتم الاحتفاظ بها)
├── gallery/ (يتم الاحتفاظ بها)
└── contactForms/ (يتم الاحتفاظ بها)
```

## المميزات الجديدة

### ✅ **إدارة البيانات الافتراضية**:

#### 1. **معاينة البيانات الافتراضية**:
- عرض جميع البيانات التي سيتم تطبيقها
- تنظيم البيانات حسب الأقسام
- معاينة مختصرة للنصوص الطويلة

#### 2. **إعادة تعيين البيانات**:
- إعادة تعيين جميع الأقسام الأساسية
- الاحتفاظ بالفروع والمعرض والرسائل
- تأكيد قبل التنفيذ
- تسجيل تاريخ الإعادة

#### 3. **تصدير البيانات**:
- تصدير البيانات الحالية
- تنسيق JSON منظم
- إمكانية النسخ أو التحميل
- معلومات التصدير (تاريخ، مصدر)

### 🎯 **البيانات الافتراضية المنظمة**:

#### **معلومات الشركة الرئيسية**:
```json
{
  "title": "AL-SALAMAT",
  "subtitle": "شركة السلامات للخدمات المتكاملة",
  "description": "نحن شركة رائدة في تقديم الخدمات المتكاملة والحلول المبتكرة..."
}
```

#### **قسم "من نحن"**:
```json
{
  "title": "من نحن",
  "description": "شركة السلامات هي شركة رائدة في مجال الخدمات المتكاملة..."
}
```

#### **قسم "اتصل بنا"**:
```json
{
  "title": "اتصل بنا",
  "infoTitle": "معلومات التواصل",
  "address": "المملكة العربية السعودية، الرياض",
  "hours": "السبت - الخميس: 8:00 ص - 10:00 م | الجمعة: مغلق"
}
```

#### **إعدادات الموقع**:
```json
{
  "maintenance": false,
  "contactEmail": "<EMAIL>",
  "contactPhone": "+966501234567"
}
```

## واجهة المستخدم

### 📋 **في لوحة الإدارة (admin.html)**:

#### **قسم إدارة البيانات الافتراضية**:
- 📋 **معاينة البيانات الافتراضية**: عرض البيانات قبل التطبيق
- 🔄 **إعادة تعيين البيانات الافتراضية**: تطبيق البيانات الافتراضية
- 📤 **تصدير البيانات الحالية**: حفظ البيانات الحالية

#### **قسم معاينة البيانات**:
- عرض تفصيلي للبيانات
- تنظيم حسب الأقسام
- معلومات التحديث والإصدار

### 🧪 **صفحة الاختبار (test-default-data.html)**:

#### **أدوات الاختبار**:
- 🔧 **تهيئة النظام**: اختبار الاتصال بـ Firebase
- 📋 **معاينة البيانات الافتراضية**: عرض البيانات المحددة مسبقاً
- 📤 **تصدير البيانات الحالية**: حفظ البيانات الموجودة
- 🔄 **إعادة تعيين البيانات**: تطبيق البيانات الافتراضية
- 🔍 **فحص البيانات الحالية**: عرض البيانات الموجودة
- 📊 **مقارنة البيانات**: مقارنة الحالية مع الافتراضية

## كيفية الاستخدام

### 👨‍💼 **للمديرين**:

#### **إعادة تعيين البيانات**:
1. **اذهب إلى لوحة الإدارة** → الإعدادات
2. **اضغط "معاينة البيانات الافتراضية"** لرؤية البيانات
3. **اضغط "إعادة تعيين البيانات الافتراضية"**
4. **أكد العملية** في النافذة المنبثقة
5. **تحقق من الصفحة الرئيسية** لرؤية التحديثات

#### **تصدير البيانات الحالية**:
1. **اضغط "تصدير البيانات الحالية"**
2. **انتظر حتى يتم التصدير**
3. **اضغط "نسخ البيانات"** أو **"تحميل كملف"**

### 🧪 **للمطورين**:

#### **استخدام صفحة الاختبار**:
1. **افتح `test-default-data.html`**
2. **اضغط "تهيئة Firebase"** للاتصال
3. **استخدم الأدوات المختلفة** للاختبار
4. **راقب النتائج** في كل قسم

## الأمان والحماية

### 🔒 **إجراءات الأمان**:

#### **تأكيد العمليات**:
- تأكيد قبل إعادة التعيين
- رسائل تحذيرية واضحة
- عدم إمكانية التراجع

#### **الاحتفاظ بالبيانات المهمة**:
- ✅ الفروع محفوظة
- ✅ المعرض محفوظ  
- ✅ رسائل التواصل محفوظة
- ❌ البيانات الأساسية يتم إعادة تعيينها

#### **تسجيل العمليات**:
- تاريخ التحديث (`updatedAt`)
- تاريخ الإعادة (`resetAt`)
- معرف المستخدم (`updatedBy`)
- إصدار البيانات (`version`)

## استكشاف الأخطاء

### 🔧 **المشاكل الشائعة**:

#### **فشل الاتصال بـ Firebase**:
```javascript
// تحقق من الاتصال
firebase.database().ref('.info/connected').on('value', (snap) => {
    console.log('Connected:', snap.val());
});
```

#### **فشل إعادة التعيين**:
- تحقق من صلاحيات المستخدم
- تأكد من صحة بنية البيانات
- راجع Console للأخطاء

#### **مشاكل التصدير**:
- تحقق من وجود البيانات
- تأكد من دعم المتصفح للتحميل
- راجع إعدادات الحماية

### 📊 **مراقبة النظام**:

#### **في Developer Tools**:
```javascript
// فحص البيانات الافتراضية
console.log(DEFAULT_WEBSITE_DATA);

// فحص البيانات المصدرة
console.log(window.exportedData);

// فحص حالة Firebase
firebase.database().ref('.info/connected').once('value', (snap) => {
    console.log('Firebase Status:', snap.val());
});
```

## التطوير المستقبلي

### 🚀 **مميزات مخططة**:

1. **استيراد البيانات**: إمكانية استيراد بيانات من ملف
2. **نسخ احتياطية تلقائية**: حفظ تلقائي قبل التغييرات
3. **قوالب متعددة**: قوالب مختلفة للبيانات الافتراضية
4. **تاريخ التغييرات**: سجل كامل للتعديلات
5. **استعادة نقطة زمنية**: العودة لحالة سابقة

### 🔧 **تحسينات تقنية**:

1. **ضغط البيانات**: تقليل حجم البيانات المخزنة
2. **تشفير البيانات الحساسة**: حماية إضافية
3. **تحسين الأداء**: تحميل أسرع للبيانات
4. **دعم اللغات المتعددة**: قوالب بلغات مختلفة

## الخلاصة

✅ **تم إنجاز**:
- نظام بيانات افتراضية منظم
- واجهة إدارة سهلة الاستخدام
- أدوات اختبار شاملة
- حماية للبيانات المهمة
- تصدير واستيراد البيانات

🎯 **النتيجة**:
- إدارة أسهل للموقع
- بيانات منظمة ومرتبة
- إمكانية الاستعادة السريعة
- نسخ احتياطية آمنة
- تجربة مستخدم محسنة

النظام الآن جاهز للاستخدام ويوفر إدارة شاملة ومنظمة لجميع بيانات الموقع! 🌟
