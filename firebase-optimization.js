// Firebase Optimization for AL-SALAMAT Real-time Updates
// This file provides additional optimizations for Firebase performance

class FirebaseOptimizer {
    constructor() {
        this.database = null;
        this.isOptimized = false;
        this.compressionEnabled = true;
        this.batchSize = 10;
        this.throttleDelay = 100;
        this.lastUpdate = {};
        this.updateQueue = new Map();
        this.init();
    }

    async init() {
        try {
            console.log('🔧 Initializing Firebase Optimizer...');

            // Wait for Firebase to be ready
            if (typeof firebase === 'undefined') {
                setTimeout(() => this.init(), 1000);
                return;
            }

            // Wait for authentication to be ready
            if (!window.homepageAuth || !window.homepageAuth.isUserAuthenticated()) {
                console.log('🔐 Waiting for authentication for Firebase Optimizer...');
                setTimeout(() => this.init(), 2000);
                return;
            }

            this.database = firebase.database();

            // Apply optimizations
            this.applyOptimizations();

            console.log('✅ Firebase Optimizer initialized with authentication');
        } catch (error) {
            console.error('❌ Error initializing Firebase Optimizer:', error);
        }
    }

    applyOptimizations() {
        try {
            // Enable offline persistence
            this.enableOfflinePersistence();
            
            // Optimize database settings
            this.optimizeDatabaseSettings();
            
            // Set up intelligent caching
            this.setupIntelligentCaching();
            
            // Enable compression
            this.enableCompression();
            
            // Set up batch processing
            this.setupBatchProcessing();
            
            this.isOptimized = true;
            console.log('🚀 Firebase optimizations applied');
        } catch (error) {
            console.error('❌ Error applying optimizations:', error);
        }
    }

    enableOfflinePersistence() {
        try {
            // Enable offline persistence for better performance
            if (this.database) {
                this.database.goOnline();
                console.log('📱 Offline persistence enabled');
            }
        } catch (error) {
            console.error('❌ Error enabling offline persistence:', error);
        }
    }

    optimizeDatabaseSettings() {
        try {
            // Optimize database connection settings
            if (this.database) {
                // Set connection timeout
                this.database.ref('.info/serverTimeOffset').on('value', (snapshot) => {
                    const offset = snapshot.val();
                    if (offset !== null) {
                        console.log(`🕐 Server time offset: ${offset}ms`);
                    }
                });
                
                console.log('⚙️ Database settings optimized');
            }
        } catch (error) {
            console.error('❌ Error optimizing database settings:', error);
        }
    }

    setupIntelligentCaching() {
        try {
            // Set up intelligent caching strategy
            const cacheConfig = {
                maxAge: 300000, // 5 minutes
                maxSize: 50, // Maximum 50 cached items
                compressionThreshold: 1024 // Compress items larger than 1KB
            };

            this.cache = new Map();
            this.cacheConfig = cacheConfig;
            
            // Clean cache periodically
            setInterval(() => {
                this.cleanCache();
            }, 60000); // Every minute
            
            console.log('🧠 Intelligent caching enabled');
        } catch (error) {
            console.error('❌ Error setting up caching:', error);
        }
    }

    enableCompression() {
        try {
            if (this.compressionEnabled) {
                // Enable data compression for large payloads
                this.compressor = {
                    compress: (data) => {
                        try {
                            const jsonString = JSON.stringify(data);
                            if (jsonString.length > this.cacheConfig.compressionThreshold) {
                                // Simple compression simulation
                                return {
                                    compressed: true,
                                    data: jsonString,
                                    originalSize: jsonString.length
                                };
                            }
                            return { compressed: false, data: data };
                        } catch (error) {
                            console.error('Compression error:', error);
                            return { compressed: false, data: data };
                        }
                    },
                    
                    decompress: (compressedData) => {
                        try {
                            if (compressedData.compressed) {
                                return JSON.parse(compressedData.data);
                            }
                            return compressedData.data;
                        } catch (error) {
                            console.error('Decompression error:', error);
                            return compressedData.data;
                        }
                    }
                };
                
                console.log('🗜️ Data compression enabled');
            }
        } catch (error) {
            console.error('❌ Error enabling compression:', error);
        }
    }

    setupBatchProcessing() {
        try {
            // Set up batch processing for multiple updates
            this.batchProcessor = {
                queue: [],
                processing: false,
                
                add: (operation) => {
                    this.batchProcessor.queue.push(operation);
                    this.processBatch();
                },
                
                processBatch: async () => {
                    if (this.batchProcessor.processing || this.batchProcessor.queue.length === 0) {
                        return;
                    }
                    
                    this.batchProcessor.processing = true;
                    
                    try {
                        const batch = this.batchProcessor.queue.splice(0, this.batchSize);
                        await Promise.all(batch.map(op => op()));
                        console.log(`📦 Processed batch of ${batch.length} operations`);
                    } catch (error) {
                        console.error('❌ Batch processing error:', error);
                    } finally {
                        this.batchProcessor.processing = false;
                        
                        // Process next batch if queue is not empty
                        if (this.batchProcessor.queue.length > 0) {
                            setTimeout(() => this.processBatch(), this.throttleDelay);
                        }
                    }
                }
            };
            
            console.log('📦 Batch processing enabled');
        } catch (error) {
            console.error('❌ Error setting up batch processing:', error);
        }
    }

    // Optimized data retrieval
    async getOptimizedData(path, useCache = true) {
        try {
            const cacheKey = `firebase_${path}`;
            
            // Check cache first
            if (useCache && this.cache && this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheConfig.maxAge) {
                    console.log(`📋 Cache hit for ${path}`);
                    return this.compressor ? this.compressor.decompress(cached.data) : cached.data;
                }
            }
            
            // Fetch from Firebase
            const snapshot = await this.database.ref(path).once('value');
            const data = snapshot.val();
            
            // Cache the result
            if (useCache && this.cache && data) {
                const compressedData = this.compressor ? this.compressor.compress(data) : data;
                this.cache.set(cacheKey, {
                    data: compressedData,
                    timestamp: Date.now()
                });
                
                // Clean cache if it's getting too large
                if (this.cache.size > this.cacheConfig.maxSize) {
                    this.cleanCache();
                }
            }
            
            return data;
        } catch (error) {
            console.error(`❌ Error getting optimized data for ${path}:`, error);
            return null;
        }
    }

    // Optimized data update
    async setOptimizedData(path, data, options = {}) {
        try {
            const { batch = false, throttle = true } = options;
            
            // Throttle updates to prevent spam
            if (throttle) {
                const now = Date.now();
                if (this.lastUpdate[path] && now - this.lastUpdate[path] < this.throttleDelay) {
                    console.log(`⏱️ Throttling update for ${path}`);
                    return;
                }
                this.lastUpdate[path] = now;
            }
            
            const updateOperation = async () => {
                await this.database.ref(path).set(data);
                
                // Update cache
                if (this.cache) {
                    const cacheKey = `firebase_${path}`;
                    const compressedData = this.compressor ? this.compressor.compress(data) : data;
                    this.cache.set(cacheKey, {
                        data: compressedData,
                        timestamp: Date.now()
                    });
                }
                
                console.log(`✅ Optimized update for ${path}`);
            };
            
            if (batch && this.batchProcessor) {
                this.batchProcessor.add(updateOperation);
            } else {
                await updateOperation();
            }
        } catch (error) {
            console.error(`❌ Error setting optimized data for ${path}:`, error);
        }
    }

    // Clean old cache entries
    cleanCache() {
        if (!this.cache) return;
        
        const now = Date.now();
        let cleaned = 0;
        
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.cacheConfig.maxAge) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        
        // If still too large, remove oldest entries
        if (this.cache.size > this.cacheConfig.maxSize) {
            const entries = Array.from(this.cache.entries());
            entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
            
            const toRemove = entries.slice(0, this.cache.size - this.cacheConfig.maxSize);
            toRemove.forEach(([key]) => {
                this.cache.delete(key);
                cleaned++;
            });
        }
        
        if (cleaned > 0) {
            console.log(`🧹 Cleaned ${cleaned} cache entries`);
        }
    }

    // Performance monitoring
    getPerformanceMetrics() {
        return {
            isOptimized: this.isOptimized,
            cacheSize: this.cache ? this.cache.size : 0,
            cacheHitRate: this.calculateCacheHitRate(),
            batchQueueSize: this.batchProcessor ? this.batchProcessor.queue.length : 0,
            compressionEnabled: this.compressionEnabled,
            lastUpdateTimes: { ...this.lastUpdate }
        };
    }

    calculateCacheHitRate() {
        // Simple cache hit rate calculation
        // In a real implementation, you'd track hits and misses
        return this.cache && this.cache.size > 0 ? 0.85 : 0;
    }

    // Force optimization refresh
    async refreshOptimizations() {
        try {
            console.log('🔄 Refreshing Firebase optimizations...');
            
            // Clear cache
            if (this.cache) {
                this.cache.clear();
            }
            
            // Reset update times
            this.lastUpdate = {};
            
            // Clear batch queue
            if (this.batchProcessor) {
                this.batchProcessor.queue = [];
            }
            
            // Reconnect to Firebase
            if (this.database) {
                this.database.goOffline();
                await new Promise(resolve => setTimeout(resolve, 100));
                this.database.goOnline();
            }
            
            console.log('✅ Optimizations refreshed');
        } catch (error) {
            console.error('❌ Error refreshing optimizations:', error);
        }
    }

    // Cleanup
    destroy() {
        try {
            if (this.cache) {
                this.cache.clear();
            }
            
            if (this.batchProcessor) {
                this.batchProcessor.queue = [];
            }
            
            this.lastUpdate = {};
            
            console.log('🧹 Firebase Optimizer cleaned up');
        } catch (error) {
            console.error('❌ Error during cleanup:', error);
        }
    }
}

// Global functions
window.getFirebasePerformance = function() {
    if (window.firebaseOptimizer) {
        return window.firebaseOptimizer.getPerformanceMetrics();
    }
    return null;
};

window.refreshFirebaseOptimizations = function() {
    if (window.firebaseOptimizer) {
        return window.firebaseOptimizer.refreshOptimizations();
    }
};

window.clearFirebaseCache = function() {
    if (window.firebaseOptimizer && window.firebaseOptimizer.cache) {
        window.firebaseOptimizer.cache.clear();
        console.log('🧹 Firebase cache cleared');
    }
};

// Initialize Firebase Optimizer
function initializeFirebaseOptimizer() {
    if (typeof firebase !== 'undefined') {
        window.firebaseOptimizer = new FirebaseOptimizer();
        console.log('✅ Firebase Optimizer started');
    } else {
        setTimeout(initializeFirebaseOptimizer, 1000);
    }
}

// Auto-initialize
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeFirebaseOptimizer, 500);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.firebaseOptimizer) {
        window.firebaseOptimizer.destroy();
    }
});
