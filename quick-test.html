<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار سريع - نظام AL-SALAMAT</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #333;
        }
        
        .test-step {
            margin: 1.5rem 0;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 8px;
            border-right: 4px solid #667eea;
        }
        
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            transition: transform 0.3s ease;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
        }
        
        .test-result {
            background: #e9ecef;
            border-radius: 6px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 150px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        
        .status {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status.success { background: #28a745; }
        .status.error { background: #dc3545; }
        .status.warning { background: #ffc107; }
        
        .progress {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>🧪 اختبار سريع لنظام AL-SALAMAT</h1>
            <p>اختبار شامل للتأكد من عمل جميع المكونات</p>
            <div class="progress">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
        </div>

        <!-- Step 1: Firebase Connection -->
        <div class="test-step">
            <h3>1️⃣ اختبار اتصال Firebase</h3>
            <p>فحص الاتصال بقاعدة البيانات والمصادقة</p>
            <button class="test-button" onclick="testFirebaseConnection()">🔥 اختبار Firebase</button>
            <div class="test-result" id="firebase-result">انقر على الزر لبدء الاختبار...</div>
        </div>

        <!-- Step 2: Authentication -->
        <div class="test-step">
            <h3>2️⃣ اختبار المصادقة</h3>
            <p>فحص نظام المصادقة للصفحة الرئيسية ولوحة الأدمن</p>
            <button class="test-button" onclick="testAuthentication()">🔐 اختبار المصادقة</button>
            <div class="test-result" id="auth-result">انقر على الزر لبدء الاختبار...</div>
        </div>

        <!-- Step 3: Data Loading -->
        <div class="test-step">
            <h3>3️⃣ اختبار تحميل البيانات</h3>
            <p>فحص تحميل البيانات من قاعدة البيانات</p>
            <button class="test-button" onclick="testDataLoading()">📊 اختبار البيانات</button>
            <div class="test-result" id="data-result">انقر على الزر لبدء الاختبار...</div>
        </div>

        <!-- Step 4: Real-time Updates -->
        <div class="test-step">
            <h3>4️⃣ اختبار التحديثات اللحظية</h3>
            <p>فحص التحديثات اللحظية والمزامنة</p>
            <button class="test-button" onclick="testRealtimeUpdates()">⚡ اختبار التحديثات</button>
            <div class="test-result" id="realtime-result">انقر على الزر لبدء الاختبار...</div>
        </div>

        <!-- Step 5: Admin Panel -->
        <div class="test-step">
            <h3>5️⃣ اختبار لوحة الأدمن</h3>
            <p>فحص وظائف لوحة الإدارة</p>
            <button class="test-button" onclick="openAdminPanel()">🛠️ فتح لوحة الأدمن</button>
            <button class="test-button" onclick="testAdminFunctions()">🔧 اختبار الوظائف</button>
            <div class="test-result" id="admin-result">انقر على الزر لبدء الاختبار...</div>
        </div>

        <!-- Final Results -->
        <div class="test-step">
            <h3>📋 النتائج النهائية</h3>
            <div id="final-results">
                <p>🔥 Firebase: <span class="status warning" id="firebase-status"></span> جاري الفحص...</p>
                <p>🔐 المصادقة: <span class="status warning" id="auth-status"></span> جاري الفحص...</p>
                <p>📊 البيانات: <span class="status warning" id="data-status"></span> جاري الفحص...</p>
                <p>⚡ التحديثات: <span class="status warning" id="realtime-status"></span> جاري الفحص...</p>
                <p>🛠️ لوحة الأدمن: <span class="status warning" id="admin-status"></span> جاري الفحص...</p>
            </div>
            <button class="test-button" onclick="runAllTests()" style="background: #28a745;">🚀 تشغيل جميع الاختبارات</button>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        const database = firebase.database();
        const auth = firebase.auth();

        let testProgress = 0;
        const totalTests = 5;

        function updateProgress() {
            testProgress++;
            const percentage = (testProgress / totalTests) * 100;
            document.getElementById('progress-bar').style.width = percentage + '%';
        }

        function updateStatus(testName, success) {
            const statusElement = document.getElementById(testName + '-status');
            if (statusElement) {
                statusElement.className = `status ${success ? 'success' : 'error'}`;
            }
        }

        async function testFirebaseConnection() {
            const result = document.getElementById('firebase-result');
            result.textContent = 'جاري فحص اتصال Firebase...\n';

            try {
                // Test Firebase initialization
                result.textContent += '✅ تم تهيئة Firebase بنجاح\n';

                // Test database connection
                const connectedRef = database.ref('.info/connected');
                const snapshot = await connectedRef.once('value');
                const connected = snapshot.val();
                result.textContent += `✅ حالة الاتصال: ${connected ? 'متصل' : 'غير متصل'}\n`;

                // Test database URL
                result.textContent += `✅ رابط قاعدة البيانات: ${firebaseConfig.databaseURL}\n`;

                updateStatus('firebase', true);
                updateProgress();
            } catch (error) {
                result.textContent += `❌ خطأ في الاتصال: ${error.message}\n`;
                updateStatus('firebase', false);
            }
        }

        async function testAuthentication() {
            const result = document.getElementById('auth-result');
            result.textContent = 'جاري فحص نظام المصادقة...\n';

            try {
                // Test anonymous authentication
                result.textContent += 'جاري اختبار المصادقة المجهولة...\n';
                const userCredential = await auth.signInAnonymously();
                result.textContent += `✅ نجحت المصادقة المجهولة: ${userCredential.user.uid}\n`;

                // Test database access with auth
                const testRef = database.ref('test-auth');
                await testRef.set({ timestamp: Date.now() });
                result.textContent += '✅ نجح الوصول لقاعدة البيانات مع المصادقة\n';

                // Clean up
                await testRef.remove();
                result.textContent += '🧹 تم تنظيف بيانات الاختبار\n';

                updateStatus('auth', true);
                updateProgress();
            } catch (error) {
                result.textContent += `❌ خطأ في المصادقة: ${error.message}\n`;
                updateStatus('auth', false);
            }
        }

        async function testDataLoading() {
            const result = document.getElementById('data-result');
            result.textContent = 'جاري فحص تحميل البيانات...\n';

            try {
                // Test loading different data sections
                const sections = ['siteContent', 'branches', 'contactSection', 'gallery', 'siteSettings'];
                
                for (const section of sections) {
                    try {
                        const snapshot = await database.ref(section).once('value');
                        const data = snapshot.val();
                        result.textContent += `✅ ${section}: ${data ? 'يحتوي على بيانات' : 'فارغ'}\n`;
                    } catch (error) {
                        result.textContent += `❌ ${section}: خطأ في التحميل\n`;
                    }
                }

                updateStatus('data', true);
                updateProgress();
            } catch (error) {
                result.textContent += `❌ خطأ في تحميل البيانات: ${error.message}\n`;
                updateStatus('data', false);
            }
        }

        async function testRealtimeUpdates() {
            const result = document.getElementById('realtime-result');
            result.textContent = 'جاري فحص التحديثات اللحظية...\n';

            try {
                // Test real-time listener
                const testRef = database.ref('test-realtime');
                
                // Set up listener
                const listenerPromise = new Promise((resolve) => {
                    testRef.on('value', (snapshot) => {
                        const data = snapshot.val();
                        if (data && data.test === 'realtime-update') {
                            result.textContent += '✅ تم استقبال التحديث اللحظي بنجاح\n';
                            testRef.off();
                            resolve();
                        }
                    });
                });

                // Send test data
                await testRef.set({
                    test: 'realtime-update',
                    timestamp: Date.now()
                });
                result.textContent += '📡 تم إرسال بيانات اختبار...\n';

                // Wait for listener
                await listenerPromise;

                // Clean up
                await testRef.remove();
                result.textContent += '🧹 تم تنظيف بيانات الاختبار\n';

                updateStatus('realtime', true);
                updateProgress();
            } catch (error) {
                result.textContent += `❌ خطأ في التحديثات اللحظية: ${error.message}\n`;
                updateStatus('realtime', false);
            }
        }

        function openAdminPanel() {
            const result = document.getElementById('admin-result');
            result.textContent = 'فتح لوحة الأدمن...\n';
            
            const adminWindow = window.open('admin.html', '_blank');
            if (adminWindow) {
                result.textContent += '✅ تم فتح لوحة الأدمن في تبويب جديد\n';
                result.textContent += 'يرجى التحقق من تحميل الصفحة وتسجيل الدخول\n';
                updateStatus('admin', true);
                updateProgress();
            } else {
                result.textContent += '❌ فشل في فتح لوحة الأدمن\n';
                updateStatus('admin', false);
            }
        }

        async function testAdminFunctions() {
            const result = document.getElementById('admin-result');
            result.textContent += '\nاختبار وظائف لوحة الأدمن...\n';

            try {
                // Check if admin files exist
                const adminFiles = ['admin.html', 'admin-styles.css', 'admin-script.js'];
                
                for (const file of adminFiles) {
                    try {
                        const response = await fetch(file, { method: 'HEAD' });
                        if (response.ok) {
                            result.textContent += `✅ ${file}: موجود\n`;
                        } else {
                            result.textContent += `❌ ${file}: غير موجود\n`;
                        }
                    } catch (error) {
                        result.textContent += `❌ ${file}: خطأ في الفحص\n`;
                    }
                }

                // Test admin authentication
                result.textContent += 'اختبار مصادقة المدير...\n';
                try {
                    const adminCredential = await auth.signInWithEmailAndPassword('<EMAIL>', 'admin123456');
                    result.textContent += '✅ نجحت مصادقة المدير\n';
                } catch (error) {
                    if (error.code === 'auth/user-not-found') {
                        result.textContent += '⚠️ حساب المدير غير موجود - سيتم إنشاؤه تلقائياً\n';
                    } else {
                        result.textContent += `❌ خطأ في مصادقة المدير: ${error.message}\n`;
                    }
                }

            } catch (error) {
                result.textContent += `❌ خطأ في اختبار وظائف الأدمن: ${error.message}\n`;
            }
        }

        async function runAllTests() {
            testProgress = 0;
            document.getElementById('progress-bar').style.width = '0%';
            
            // Reset all status indicators
            ['firebase', 'auth', 'data', 'realtime', 'admin'].forEach(test => {
                updateStatus(test, false);
                document.getElementById(test + '-status').className = 'status warning';
            });

            // Run tests sequentially
            await testFirebaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testAuthentication();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testDataLoading();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testRealtimeUpdates();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            openAdminPanel();
            
            // Show completion message
            setTimeout(() => {
                alert('تم الانتهاء من جميع الاختبارات! تحقق من النتائج أعلاه.');
            }, 2000);
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                testFirebaseConnection();
            }, 1000);
        });
    </script>
</body>
</html>
