# إدارة أقسام الموقع من Firebase

## نظرة عامة
تم تطوير نظام شامل لإدارة جميع أقسام الموقع (من نحن، فروعنا، اتصل بنا) من خلال صفحة الإدارة مع تحديث فوري في الصفحة الرئيسية.

## الأقسام المدارة ديناميكياً

### 🏠 **قسم معلومات الشركة الرئيسية**
- **العنوان الرئيسي**: عنوان الشركة في أعلى الصفحة
- **العنوان الفرعي**: الوصف المختصر تحت العنوان
- **الوصف**: نص مختصر في الصفحة الرئيسية

### 📖 **قسم "من نحن"**
- **عنوان القسم**: يمكن تغييره من "من نحن" إلى أي عنوان آخر
- **محتوى القسم**: النص الكامل لقسم "من نحن"
- **تحديث فوري**: يظهر التحديث مباشرة في الصفحة الرئيسية

### 🏢 **قسم "فروعنا"**
- **عنوان القسم**: يمكن تغييره من "فروعنا" إلى أي عنوان آخر
- **الفروع**: إدارة كاملة للفروع (إضافة/تعديل/حذف)
- **أزرار الموقع**: تفتح خرائط Google Maps

### 📞 **قسم "اتصل بنا"**
- **عنوان القسم**: يمكن تغييره من "اتصل بنا"
- **عنوان معلومات التواصل**: عنوان فرعي للمعلومات
- **العنوان**: عنوان الشركة الفيزيائي
- **ساعات العمل**: أوقات العمل الرسمية
- **معلومات التواصل**: البريد والهاتف (من الإعدادات)

## هيكل قاعدة البيانات

### Firebase Realtime Database Structure:
```
al-salamat/
├── siteContent/
│   ├── title: "AL-SALAMAT"
│   ├── subtitle: "رائدة في زجاج السيارات"
│   ├── description: "وصف مختصر للصفحة الرئيسية"
│   └── updatedAt: "timestamp"
│
├── aboutSection/
│   ├── title: "من نحن"
│   ├── description: "محتوى قسم من نحن الكامل"
│   └── updatedAt: "timestamp"
│
├── branchesSection/
│   ├── title: "فروعنا"
│   └── updatedAt: "timestamp"
│
├── contactSection/
│   ├── title: "اتصل بنا"
│   ├── infoTitle: "معلومات التواصل"
│   ├── address: "العنوان الفيزيائي"
│   ├── hours: "ساعات العمل"
│   └── updatedAt: "timestamp"
│
├── branches/
│   └── [branch-id]/
│       ├── name: "اسم الفرع"
│       ├── address: "عنوان الفرع"
│       └── phone: "رقم الهاتف"
│
└── siteSettings/
    ├── contactEmail: "البريد الإلكتروني"
    ├── contactPhone: "رقم الهاتف"
    └── updatedAt: "timestamp"
```

## واجهة الإدارة

### صفحة الإدارة (admin.html)
تحتوي على نماذج منفصلة لكل قسم:

#### 1. **نموذج معلومات الشركة**
- عنوان الشركة
- العنوان الفرعي  
- وصف الشركة (للصفحة الرئيسية)

#### 2. **نموذج قسم "من نحن"**
- عنوان القسم
- محتوى القسم (نص طويل)

#### 3. **نموذج قسم "فروعنا"**
- عنوان قسم الفروع

#### 4. **نموذج قسم "اتصل بنا"**
- عنوان قسم الاتصال
- عنوان معلومات التواصل
- العنوان الفيزيائي
- ساعات العمل

#### 5. **إدارة الفروع** (منفصلة)
- إضافة فروع جديدة
- تعديل الفروع الموجودة
- حذف الفروع

#### 6. **إعدادات الموقع** (منفصلة)
- البريد الإلكتروني
- رقم الهاتف

## التحديث الديناميكي

### كيفية العمل:
1. **المدير يدخل لوحة الإدارة**
2. **يعدل أي محتوى في النماذج**
3. **يضغط "حفظ"**
4. **التغييرات تُحفظ في Firebase فوراً**
5. **الصفحة الرئيسية تتلقى التحديث تلقائياً**
6. **المحتوى يتحدث بدون إعادة تحميل**

### العناصر المحدثة تلقائياً:
- ✅ عنوان الشركة (`#company-title`)
- ✅ العنوان الفرعي (`#company-subtitle`)
- ✅ وصف الشركة (`#company-description`)
- ✅ عنوان قسم "من نحن" (`#about-title`)
- ✅ محتوى قسم "من نحن" (`#about-description`)
- ✅ عنوان قسم الفروع (`#branches-title`)
- ✅ قائمة الفروع (`#dynamic-branches`)
- ✅ عنوان قسم الاتصال (`#contact-title`)
- ✅ عنوان معلومات التواصل (`#contact-info-title`)
- ✅ العنوان الفيزيائي (`#contact-address-display`)
- ✅ ساعات العمل (`#contact-hours-display`)
- ✅ البريد الإلكتروني (`#contact-email-display`)
- ✅ رقم الهاتف (`#contact-phone-display`)

## الملفات المحدثة

### 1. **index.html**
- إضافة معرفات (IDs) لجميع العناصر القابلة للتحديث
- تنظيم أفضل للأقسام

### 2. **admin.html**
- نماذج منفصلة لكل قسم
- واجهة سهلة الاستخدام
- تنظيم منطقي للمحتوى

### 3. **admin.js**
- وظائف حفظ وتحميل لكل قسم
- معالجة أخطاء شاملة
- رسائل نجاح واضحة

### 4. **dynamic-content.js**
- مستمعات للتحديث الفوري
- وظائف تحديث لكل قسم
- رسوم متحركة عند التحديث

### 5. **database-rules.json**
- قواعد أمان للأقسام الجديدة
- صلاحيات مناسبة

### 6. **test-dynamic.html**
- اختبارات شاملة للأقسام الجديدة
- معاينة مباشرة للتحديثات

## المميزات التقنية

### 🔥 **Firebase Realtime Database**
- تحديث فوري بدون تأخير
- مزامنة تلقائية عبر جميع الأجهزة
- موثوقية عالية

### 🎨 **التأثيرات البصرية**
- رسوم متحركة عند التحديث
- مؤشر بصري للتحديثات
- انتقالات سلسة

### 🛡️ **الأمان**
- تنظيف HTML لمنع XSS
- فحص صحة البيانات
- صلاحيات محددة

### 📱 **الاستجابة**
- يعمل على جميع الأجهزة
- تصميم متجاوب
- أداء محسن

## الاستخدام العملي

### للمديرين:
1. **سجل دخول** بحساب `<EMAIL>`
2. **اذهب إلى قسم "إدارة المحتوى"**
3. **عدّل أي قسم تريده**:
   - معلومات الشركة
   - قسم "من نحن"
   - عنوان قسم الفروع
   - قسم "اتصل بنا"
4. **احفظ التغييرات**
5. **شاهد التحديث الفوري** في الصفحة الرئيسية

### للزوار:
- يرون المحتوى المحدث فوراً
- تجربة سلسة ومحدثة
- محتوى دائماً حديث

## الاختبار

### استخدام صفحة الاختبار:
1. افتح `test-dynamic.html`
2. اضغط "تهيئة الاختبار"
3. جرب اختبار كل قسم:
   - اختبار قسم "من نحن"
   - اختبار الفروع
   - اختبار قسم الاتصال
4. شاهد التحديث الفوري في المعاينة

### اختبار حقيقي:
1. افتح الصفحة الرئيسية في تبويب
2. افتح لوحة الإدارة في تبويب آخر
3. عدّل أي محتوى في لوحة الإدارة
4. شاهد التحديث الفوري في الصفحة الرئيسية

## الخلاصة
تم تطوير نظام إدارة محتوى شامل وديناميكي يجعل جميع أقسام الموقع قابلة للتحديث من صفحة الإدارة مع تحديث فوري في الصفحة الرئيسية. النظام يوفر مرونة كاملة في إدارة المحتوى مع تجربة مستخدم محسنة.
