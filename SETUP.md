# إعداد وتشغيل موقع AL-SALAMAT مع Firebase

## 🚀 التشغيل السريع

### الطريقة الأولى: استخدام Live Server (الأسهل)

1. **تثبيت Live Server Extension في VS Code**
2. **فتح المجلد في VS Code**
3. **النقر بالزر الأيمن على `index.html`**
4. **اختيار "Open with Live Server"**

### الطريقة الثانية: استخدام Python

```bash
# Python 3
python -m http.server 3000

# Python 2
python -m SimpleHTTPServer 3000
```

ثم فتح: `http://localhost:3000`

### الطريقة الثالثة: استخدام Node.js

```bash
npm install -g http-server
http-server . -p 3000 -o
```

## 📁 الملفات المهمة

- `index.html` - الصفحة الرئيسية
- `login-cdn.html` - صفحة التسجيل (تعمل مع Firebase)
- `styles.css` - ملف التصميم
- `firebase-config.js` - إعدادات Firebase (للمطورين)

## 🔥 Firebase Database Structure

سيتم إنشاء البيانات في Realtime Database كالتالي:

```
al-salamat-default-rtdb/
├── users/
│   └── [user-uid]/
│       ├── name: "اسم المستخدم"
│       ├── email: "<EMAIL>"
│       ├── phone: "رقم الهاتف"
│       ├── createdAt: "2024-01-01T00:00:00.000Z"
│       ├── role: "user"
│       └── uid: "user-uid"
├── usersList/
│   └── [auto-generated-key]/
│       ├── uid: "user-uid"
│       ├── name: "اسم المستخدم"
│       ├── email: "<EMAIL>"
│       ├── phone: "رقم الهاتف"
│       └── createdAt: "2024-01-01T00:00:00.000Z"
└── loginActivity/
    └── [auto-generated-key]/
        ├── uid: "user-uid"
        ├── email: "<EMAIL>"
        ├── loginTime: "2024-01-01T00:00:00.000Z"
        └── userAgent: "browser info"
```

## ✅ الوظائف المتاحة

### تسجيل حساب جديد:
- ✅ إنشاء حساب في Firebase Authentication
- ✅ حفظ البيانات في Realtime Database
- ✅ التحقق من تطابق كلمة المرور
- ✅ رسائل خطأ باللغة العربية

### تسجيل الدخول:
- ✅ تسجيل دخول بالبريد وكلمة المرور
- ✅ حفظ نشاط تسجيل الدخول
- ✅ حفظ بيانات المستخدم في localStorage

## 🔧 إعدادات Firebase

الموقع مُعد للعمل مع:
- **Database URL:** https://al-salamat-default-rtdb.firebaseio.com
- **Project ID:** al-salamat
- **Auth Domain:** al-salamat.firebaseapp.com

## 🐛 حل المشاكل الشائعة

### مشكلة CORS:
- استخدم `login-cdn.html` بدلاً من `login.html`
- تأكد من تشغيل الموقع على server وليس file://

### مشكلة Firebase:
- تأكد من اتصال الإنترنت
- تحقق من إعدادات Firebase في console

### مشكلة الأذونات:
- تأكد من تفعيل Authentication في Firebase Console
- تأكد من تفعيل Realtime Database

## 📞 الدعم

للمساعدة أو الاستفسارات، تواصل مع فريق التطوير.
