<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التحديثات اللحظية - AL-SALAMAT</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="realtime-styles.css">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
            min-height: 100vh;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .test-button.danger {
            background: #f44336;
        }
        
        .test-button.danger:hover {
            background: #d32f2f;
        }
        
        .test-button.warning {
            background: #ff9800;
        }
        
        .test-button.warning:hover {
            background: #f57c00;
        }
        
        .test-result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-online { background: #4CAF50; }
        .status-offline { background: #f44336; }
        .status-syncing { background: #ff9800; animation: pulse 1s infinite; }
        
        .test-data {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .data-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
        }
        
        .data-card h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        
        .data-value {
            background: white;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 8px;
            margin: 5px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🧪 اختبار التحديثات اللحظية - AL-SALAMAT</h1>
        
        <!-- Connection Status Test -->
        <div class="test-section">
            <h2 class="test-title">
                حالة الاتصال 
                <span class="status-indicator" id="connection-indicator"></span>
            </h2>
            
            <div class="test-buttons">
                <button class="test-button" onclick="testConnection()">فحص الاتصال</button>
                <button class="test-button" onclick="testFirebaseConnection()">فحص Firebase</button>
                <button class="test-button warning" onclick="simulateOffline()">محاكاة انقطاع الاتصال</button>
                <button class="test-button" onclick="simulateOnline()">محاكاة استعادة الاتصال</button>
            </div>
            
            <div class="test-result" id="connection-result">
                جاري فحص حالة الاتصال...
            </div>
        </div>
        
        <!-- Real-time Updates Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار التحديثات اللحظية</h2>
            
            <div class="test-buttons">
                <button class="test-button" onclick="testCompanyInfoUpdate()">تحديث معلومات الشركة</button>
                <button class="test-button" onclick="testBranchesUpdate()">تحديث الفروع</button>
                <button class="test-button" onclick="testContactUpdate()">تحديث معلومات التواصل</button>
                <button class="test-button" onclick="testGalleryUpdate()">تحديث المعرض</button>
                <button class="test-button warning" onclick="forceRefreshAll()">تحديث شامل</button>
            </div>
            
            <div class="test-result" id="updates-result">
                انقر على أي زر لاختبار التحديثات...
            </div>
        </div>
        
        <!-- Cache Management Test -->
        <div class="test-section">
            <h2 class="test-title">إدارة الذاكرة المؤقتة</h2>
            
            <div class="test-buttons">
                <button class="test-button" onclick="showCacheInfo()">عرض معلومات الذاكرة المؤقتة</button>
                <button class="test-button warning" onclick="clearCache()">مسح الذاكرة المؤقتة</button>
                <button class="test-button danger" onclick="clearAllStorage()">مسح جميع البيانات المحلية</button>
            </div>
            
            <div class="test-result" id="cache-result">
                انقر على "عرض معلومات الذاكرة المؤقتة" لرؤية التفاصيل...
            </div>
        </div>
        
        <!-- Performance Test -->
        <div class="test-section">
            <h2 class="test-title">اختبار الأداء</h2>
            
            <div class="test-buttons">
                <button class="test-button" onclick="measureLoadTime()">قياس وقت التحميل</button>
                <button class="test-button" onclick="measureUpdateTime()">قياس وقت التحديث</button>
                <button class="test-button" onclick="stressTest()">اختبار الضغط</button>
                <button class="test-button" onclick="memoryUsage()">استهلاك الذاكرة</button>
            </div>
            
            <div class="test-result" id="performance-result">
                انقر على أي زر لاختبار الأداء...
            </div>
        </div>
        
        <!-- Live Data Display -->
        <div class="test-section">
            <h2 class="test-title">البيانات المباشرة</h2>
            
            <div class="test-data" id="live-data">
                <!-- سيتم ملء البيانات هنا -->
            </div>
        </div>
        
        <!-- Console Logs -->
        <div class="test-section">
            <h2 class="test-title">سجل الأحداث المباشر</h2>
            <div class="test-result" id="console-logs" style="height: 300px;">
                <!-- سيتم عرض السجلات هنا -->
            </div>
        </div>
    </div>

    <!-- Firebase CDN -->
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/10.7.1/firebase-database-compat.js"></script>
    
    <!-- Our Scripts -->
    <script src="enhanced-realtime-updates.js"></script>
    <script src="connection-manager.js"></script>
    
    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
            authDomain: "al-salamat.firebaseapp.com",
            databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
            projectId: "al-salamat",
            storageBucket: "al-salamat.firebasestorage.app",
            messagingSenderId: "108512109295",
            appId: "1:108512109295:web:84f99d95019e2101dcb11a"
        };

        // Initialize Firebase
        firebase.initializeApp(firebaseConfig);
        
        // Test functions
        let testStartTime = Date.now();
        let logContainer = document.getElementById('console-logs');
        
        // Override console.log to capture logs
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLogEntry('LOG', args.join(' '));
        };
        
        const originalError = console.error;
        console.error = function(...args) {
            originalError.apply(console, args);
            addLogEntry('ERROR', args.join(' '));
        };
        
        function addLogEntry(type, message) {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                margin: 2px 0;
                padding: 5px;
                border-radius: 3px;
                font-size: 11px;
                ${type === 'ERROR' ? 'background: #ffebee; color: #c62828;' : 'background: #f3f4f6; color: #374151;'}
            `;
            logEntry.innerHTML = `<strong>[${timestamp}] ${type}:</strong> ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // Test functions
        function testConnection() {
            const result = document.getElementById('connection-result');
            result.innerHTML = 'جاري فحص الاتصال...\n';
            
            const isOnline = navigator.onLine;
            const connectionInfo = window.getConnectionStatus ? window.getConnectionStatus() : null;
            
            result.innerHTML += `حالة الشبكة: ${isOnline ? '✅ متصل' : '❌ غير متصل'}\n`;
            
            if (connectionInfo) {
                result.innerHTML += `Firebase: ${connectionInfo.isConnectedToFirebase ? '✅ متصل' : '❌ غير متصل'}\n`;
                result.innerHTML += `آخر مزامنة: ${connectionInfo.lastSyncTime ? new Date(connectionInfo.lastSyncTime).toLocaleString('ar-SA') : 'لم تتم بعد'}\n`;
                result.innerHTML += `محاولات إعادة الاتصال: ${connectionInfo.retryAttempts}\n`;
            }
            
            updateConnectionIndicator();
        }
        
        function testFirebaseConnection() {
            const result = document.getElementById('connection-result');
            result.innerHTML = 'جاري فحص اتصال Firebase...\n';
            
            if (typeof firebase !== 'undefined') {
                const connectedRef = firebase.database().ref('.info/connected');
                connectedRef.once('value').then((snapshot) => {
                    const connected = snapshot.val();
                    result.innerHTML += `Firebase Realtime Database: ${connected ? '✅ متصل' : '❌ غير متصل'}\n`;
                }).catch((error) => {
                    result.innerHTML += `خطأ في Firebase: ${error.message}\n`;
                });
            } else {
                result.innerHTML += '❌ Firebase غير محمل\n';
            }
        }
        
        function simulateOffline() {
            if (window.connectionManager) {
                window.connectionManager.isOnline = false;
                window.connectionManager.isConnectedToFirebase = false;
                window.connectionManager.updateConnectionStatus();
                addLogEntry('LOG', 'تم محاكاة انقطاع الاتصال');
            }
        }
        
        function simulateOnline() {
            if (window.connectionManager) {
                window.connectionManager.isOnline = true;
                window.connectionManager.attemptFirebaseReconnection();
                addLogEntry('LOG', 'تم محاكاة استعادة الاتصال');
            }
        }
        
        function testCompanyInfoUpdate() {
            const result = document.getElementById('updates-result');
            result.innerHTML = 'جاري اختبار تحديث معلومات الشركة...\n';
            
            const testData = {
                title: `AL-SALAMAT - اختبار ${Date.now()}`,
                subtitle: `رائدة في زجاج السيارات - ${new Date().toLocaleTimeString('ar-SA')}`,
                description: `وصف تجريبي محدث في ${new Date().toLocaleString('ar-SA')}`
            };
            
            if (firebase.database) {
                firebase.database().ref('siteContent').set(testData)
                    .then(() => {
                        result.innerHTML += '✅ تم تحديث معلومات الشركة بنجاح\n';
                        result.innerHTML += `البيانات الجديدة: ${JSON.stringify(testData, null, 2)}\n`;
                    })
                    .catch((error) => {
                        result.innerHTML += `❌ خطأ: ${error.message}\n`;
                    });
            }
        }
        
        function testBranchesUpdate() {
            const result = document.getElementById('updates-result');
            result.innerHTML = 'جاري اختبار تحديث الفروع...\n';
            
            const testBranch = {
                name: `فرع تجريبي ${Date.now()}`,
                address: `عنوان تجريبي - ${new Date().toLocaleString('ar-SA')}`,
                phone: '+966501234567',
                createdAt: new Date().toISOString()
            };
            
            if (firebase.database) {
                const branchId = `test_branch_${Date.now()}`;
                firebase.database().ref(`branches/${branchId}`).set(testBranch)
                    .then(() => {
                        result.innerHTML += '✅ تم إضافة فرع تجريبي بنجاح\n';
                        result.innerHTML += `معرف الفرع: ${branchId}\n`;
                        result.innerHTML += `البيانات: ${JSON.stringify(testBranch, null, 2)}\n`;
                    })
                    .catch((error) => {
                        result.innerHTML += `❌ خطأ: ${error.message}\n`;
                    });
            }
        }
        
        function testContactUpdate() {
            const result = document.getElementById('updates-result');
            result.innerHTML = 'جاري اختبار تحديث معلومات التواصل...\n';
            
            const testData = {
                title: 'اتصل بنا',
                infoTitle: 'معلومات التواصل المحدثة',
                address: `عنوان محدث - ${new Date().toLocaleString('ar-SA')}`,
                hours: `ساعات العمل المحدثة - ${new Date().toLocaleTimeString('ar-SA')}`
            };
            
            if (firebase.database) {
                firebase.database().ref('contactSection').set(testData)
                    .then(() => {
                        result.innerHTML += '✅ تم تحديث معلومات التواصل بنجاح\n';
                        result.innerHTML += `البيانات الجديدة: ${JSON.stringify(testData, null, 2)}\n`;
                    })
                    .catch((error) => {
                        result.innerHTML += `❌ خطأ: ${error.message}\n`;
                    });
            }
        }
        
        function forceRefreshAll() {
            const result = document.getElementById('updates-result');
            result.innerHTML = 'جاري التحديث الشامل...\n';
            
            if (window.manualRefresh) {
                window.manualRefresh();
                result.innerHTML += '✅ تم تشغيل التحديث الشامل\n';
            }
            
            if (window.enhancedRealtimeManager) {
                window.enhancedRealtimeManager.forceSync();
                result.innerHTML += '✅ تم تشغيل المزامنة المحسنة\n';
            }
        }
        
        function showCacheInfo() {
            const result = document.getElementById('cache-result');
            result.innerHTML = 'معلومات الذاكرة المؤقتة:\n\n';
            
            // LocalStorage
            result.innerHTML += `LocalStorage (${localStorage.length} عنصر):\n`;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                result.innerHTML += `  ${key}: ${value.length > 50 ? value.substring(0, 50) + '...' : value}\n`;
            }
            
            // SessionStorage
            result.innerHTML += `\nSessionStorage (${sessionStorage.length} عنصر):\n`;
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                const value = sessionStorage.getItem(key);
                result.innerHTML += `  ${key}: ${value.length > 50 ? value.substring(0, 50) + '...' : value}\n`;
            }
        }
        
        function clearCache() {
            if (window.clearAllCache) {
                window.clearAllCache();
                document.getElementById('cache-result').innerHTML = '✅ تم مسح الذاكرة المؤقتة بنجاح';
            }
        }
        
        function clearAllStorage() {
            localStorage.clear();
            sessionStorage.clear();
            document.getElementById('cache-result').innerHTML = '✅ تم مسح جميع البيانات المحلية';
        }
        
        function measureLoadTime() {
            const loadTime = Date.now() - testStartTime;
            document.getElementById('performance-result').innerHTML = `وقت تحميل الصفحة: ${loadTime} مللي ثانية`;
        }
        
        function measureUpdateTime() {
            const start = performance.now();
            if (window.enhancedRealtimeManager) {
                window.enhancedRealtimeManager.forceSync();
                const end = performance.now();
                document.getElementById('performance-result').innerHTML = `وقت التحديث: ${(end - start).toFixed(2)} مللي ثانية`;
            }
        }
        
        function updateConnectionIndicator() {
            const indicator = document.getElementById('connection-indicator');
            const isOnline = navigator.onLine;
            const connectionInfo = window.getConnectionStatus ? window.getConnectionStatus() : null;
            
            if (isOnline && connectionInfo && connectionInfo.isConnectedToFirebase) {
                indicator.className = 'status-indicator status-online';
            } else if (isOnline) {
                indicator.className = 'status-indicator status-syncing';
            } else {
                indicator.className = 'status-indicator status-offline';
            }
        }
        
        function updateLiveData() {
            const container = document.getElementById('live-data');
            const connectionInfo = window.getConnectionStatus ? window.getConnectionStatus() : {};
            
            container.innerHTML = `
                <div class="data-card">
                    <h4>حالة الاتصال</h4>
                    <div class="data-value">الشبكة: ${navigator.onLine ? 'متصل' : 'غير متصل'}</div>
                    <div class="data-value">Firebase: ${connectionInfo.isConnectedToFirebase ? 'متصل' : 'غير متصل'}</div>
                    <div class="data-value">محاولات إعادة الاتصال: ${connectionInfo.retryAttempts || 0}</div>
                </div>
                <div class="data-card">
                    <h4>الأداء</h4>
                    <div class="data-value">الذاكرة المستخدمة: ${(performance.memory ? (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2) + ' MB' : 'غير متاح')}</div>
                    <div class="data-value">وقت التشغيل: ${((Date.now() - testStartTime) / 1000).toFixed(1)} ثانية</div>
                </div>
                <div class="data-card">
                    <h4>المدراء النشطون</h4>
                    <div class="data-value">Enhanced Realtime Manager: ${window.enhancedRealtimeManager ? '✅ نشط' : '❌ غير نشط'}</div>
                    <div class="data-value">Connection Manager: ${window.connectionManager ? '✅ نشط' : '❌ غير نشط'}</div>
                    <div class="data-value">Dynamic Content Manager: ${window.dynamicContentManager ? '✅ نشط' : '❌ غير نشط'}</div>
                </div>
            `;
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            testConnection();
            updateLiveData();
            
            // Update live data every 5 seconds
            setInterval(() => {
                updateConnectionIndicator();
                updateLiveData();
            }, 5000);
        });
    </script>
</body>
</html>
