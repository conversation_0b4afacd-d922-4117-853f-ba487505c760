# تعليمات إعداد نظام AL-SALAMAT المحدث

## 🔥 إعداد Firebase Realtime Database

### 1. قواعد قاعدة البيانات
قم بتحديث قواعد Firebase Realtime Database إلى:

```json
{
  "rules": {
    ".read": "auth != null",
    ".write": "auth != null"
  }
}
```

### 2. رابط قاعدة البيانات
تأكد من أن الرابط صحيح:
```
https://al-salamat-default-rtdb.firebaseio.com/
```

## 🔐 نظام المصادقة الجديد

### للصفحة الرئيسية (index.html)
- **المصادقة التلقائية**: يتم تسجيل الدخول تلقائياً باستخدام Anonymous Authentication
- **النسخ الاحتياطي**: إذا فشلت المصادقة المجهولة، يتم إنشاء حساب تجريبي مؤقت
- **التحديثات اللحظية**: تعمل فور نجاح المصادقة

### للوحة الأدمن (admin.html)
- **حساب المدير الرئيسي**: <EMAIL>
- **كلمة المرور الافتراضية**: admin123456
- **الإنشاء التلقائي**: إذا لم يكن الحساب موجوداً، سيتم إنشاؤه تلقائياً

## 📁 الملفات الجديدة المضافة

### 1. `homepage-auth.js`
- **الوظيفة**: إدارة المصادقة للصفحة الرئيسية
- **المميزات**:
  - مصادقة تلقائية مجهولة
  - تحميل المحتوى مباشرة من Firebase
  - معالجة أخطاء المصادقة
  - إعادة المحاولة التلقائية

### 2. `admin.html` (محدث بالكامل)
- **واجهة إدارة متكاملة** مع جميع الأقسام:
  - 🏢 معلومات الشركة
  - 🏪 إدارة الفروع
  - 📞 معلومات التواصل
  - 🖼️ إدارة المعرض
  - ⚙️ إعدادات الموقع
  - 👥 إدارة المستخدمين
  - 📧 الرسائل الواردة

### 3. `admin-styles.css`
- **تصميم عصري ومتجاوب** للوحة الأدمن
- **ألوان متناسقة** مع هوية الموقع
- **رسوم متحركة** وتأثيرات بصرية

### 4. `admin-script.js`
- **نظام إدارة متكامل** مع:
  - مصادقة آمنة
  - إدارة الفروع (إضافة/تعديل/حذف)
  - رفع الصور للمعرض
  - إدارة المستخدمين
  - معالجة الرسائل

## 🚀 كيفية الاستخدام

### 1. الوصول للوحة الأدمن
1. افتح `admin.html` في المتصفح
2. سيتم تسجيل الدخول تلقائياً بحساب <EMAIL>
3. إذا لم يكن الحساب موجوداً، سيتم إنشاؤه تلقائياً

### 2. إدارة الفروع
1. اذهب إلى قسم "إدارة الفروع"
2. املأ نموذج "إضافة فرع جديد"
3. انقر "إضافة الفرع"
4. ستظهر التحديثات فوراً في الصفحة الرئيسية

### 3. إدارة المعرض
1. اذهب إلى قسم "إدارة المعرض"
2. اختر صورة (أقل من 5MB)
3. أضف وصف للصورة
4. انقر "رفع الصورة"

### 4. تحديث معلومات الشركة
1. اذهب إلى قسم "معلومات الشركة"
2. عدّل العنوان الرئيسي والفرعي والوصف
3. انقر "حفظ التغييرات"

## 🔧 استكشاف الأخطاء

### مشكلة: لا تظهر البيانات في الصفحة الرئيسية
**الحلول**:
1. تحقق من وحدة التحكم في المتصفح (F12)
2. تأكد من نجاح المصادقة: `window.getHomepageAuthStatus()`
3. أعد تحميل المحتوى: `window.reloadHomepageContent()`
4. تحقق من قواعد Firebase

### مشكلة: لا يمكن الوصول للوحة الأدمن
**الحلول**:
1. تأكد من أن Firebase Authentication مفعل
2. تحقق من قواعد قاعدة البيانات
3. امسح بيانات المتصفح وأعد المحاولة
4. تحقق من وحدة التحكم للأخطاء

### مشكلة: التحديثات لا تظهر فوراً
**الحلول**:
1. تحقق من حالة الاتصال في أسفل الصفحة
2. استخدم `window.manualRefresh()` للتحديث اليدوي
3. امسح الذاكرة المؤقتة: `window.clearAllCache()`

## 📊 مراقبة النظام

### فحص حالة المصادقة
```javascript
// للصفحة الرئيسية
console.log(window.getHomepageAuthStatus());

// للوحة الأدمن
console.log(window.getConnectionStatus());
```

### فحص البيانات المحملة
```javascript
// فحص الفروع
const branchesGrid = document.getElementById('dynamic-branches');
console.log('عدد الفروع:', branchesGrid.children.length);

// فحص المعرض
const galleryGrid = document.getElementById('dynamic-gallery');
console.log('عدد الصور:', galleryGrid.children.length);
```

### إعادة تحميل المحتوى
```javascript
// إعادة تحميل شامل
window.reloadHomepageContent();

// تحديث يدوي
window.manualRefresh();

// مسح الذاكرة المؤقتة
window.clearAllCache();
```

## 🔒 الأمان

### كلمات المرور
- **غيّر كلمة مرور المدير** في `admin-script.js` (السطر 118)
- **استخدم كلمة مرور قوية** (8 أحرف على الأقل)

### قواعد Firebase
- **لا تغيّر قواعد قاعدة البيانات** إلا إذا كنت تعرف ما تفعل
- **احتفظ بنسخة احتياطية** من القواعد الحالية

### النسخ الاحتياطي
- **احتفظ بنسخة احتياطية** من جميع الملفات
- **اختبر النظام** بانتظام للتأكد من عمله

## 📱 التوافق

### المتصفحات المدعومة
- ✅ Chrome (الأحدث)
- ✅ Firefox (الأحدث)
- ✅ Safari (الأحدث)
- ✅ Edge (الأحدث)

### الأجهزة المدعومة
- ✅ أجهزة الكمبيوتر المكتبية
- ✅ الأجهزة اللوحية
- ✅ الهواتف الذكية

## 🎯 الخطوات التالية

### 1. اختبار النظام
1. افتح الصفحة الرئيسية وتأكد من تحميل البيانات
2. افتح لوحة الأدمن وأضف فرع تجريبي
3. تحقق من ظهور الفرع فوراً في الصفحة الرئيسية
4. اختبر رفع صورة للمعرض

### 2. تخصيص المحتوى
1. حدّث معلومات الشركة
2. أضف الفروع الحقيقية
3. ارفع صور المعرض
4. حدّث معلومات التواصل

### 3. المراقبة والصيانة
1. راقب السجلات في وحدة التحكم
2. تحقق من أداء النظام بانتظام
3. احتفظ بنسخ احتياطية من البيانات

## 📞 الدعم الفني

### في حالة وجود مشاكل:
1. **تحقق من وحدة التحكم** (F12) للأخطاء
2. **اختبر الاتصال** بـ Firebase
3. **تأكد من صحة القواعد** في Firebase
4. **أعد تشغيل المتصفح** وحاول مرة أخرى

### معلومات مفيدة للدعم:
- رابط قاعدة البيانات: https://al-salamat-default-rtdb.firebaseio.com/
- حساب المدير: <EMAIL>
- إصدار Firebase: 10.7.1

---

**ملاحظة**: هذا النظام مصمم ليعمل بكفاءة 100% مع التحديثات اللحظية. تأكد من اتباع التعليمات بدقة للحصول على أفضل النتائج.
