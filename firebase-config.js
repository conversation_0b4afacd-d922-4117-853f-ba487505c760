// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth, createUserWithEmailAndPassword, signInWithEmailAndPassword, updateProfile } from "firebase/auth";
import { getDatabase, ref, set, push, get } from "firebase/database";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDaG5dIF-XpIviVRqZPKbg__x9Yd3pEc6o",
  authDomain: "al-salamat.firebaseapp.com",
  databaseURL: "https://al-salamat-default-rtdb.firebaseio.com",
  projectId: "al-salamat",
  storageBucket: "al-salamat.firebasestorage.app",
  messagingSenderId: "108512109295",
  appId: "1:108512109295:web:84f99d95019e2101dcb11a"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase Authentication and get a reference to the service
export const auth = getAuth(app);

// Initialize Realtime Database and get a reference to the service
export const database = getDatabase(app);

// Authentication functions
export const registerUser = async (email, password, name, phone) => {
  try {
    // Create user with email and password
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Update user profile with display name
    await updateProfile(user, {
      displayName: name
    });

    // Determine user role based on email
    const adminEmails = ['<EMAIL>'];
    const userRole = adminEmails.includes(email) ? 'admin' : 'user';

    // Save additional user data to Realtime Database with name as key
    const userRef = ref(database, 'users/' + name.replace(/\s+/g, '_'));
    await set(userRef, {
      name: name,
      email: email,
      phone: phone,
      createdAt: new Date().toISOString(),
      role: userRole,
      uid: user.uid,
      permissions: userRole === 'admin' ? {
        manageContent: true,
        manageBranches: true,
        manageUsers: true,
        manageMessages: true,
        manageGallery: true,
        manageSettings: true
      } : {
        manageContent: false,
        manageBranches: false,
        manageUsers: false,
        manageMessages: false,
        manageGallery: false,
        manageSettings: false
      }
    });

    return {
      success: true,
      user: user,
      message: "تم إنشاء الحساب بنجاح!"
    };
  } catch (error) {
    console.error("Error creating user:", error);
    return {
      success: false,
      error: error.code,
      message: getErrorMessage(error.code)
    };
  }
};

export const loginUser = async (email, password) => {
  try {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Save login activity to Realtime Database
    const loginRef = ref(database, 'loginActivity');
    const newLoginRef = push(loginRef);
    await set(newLoginRef, {
      uid: user.uid,
      email: user.email,
      loginTime: new Date().toISOString(),
      userAgent: navigator.userAgent
    });

    return {
      success: true,
      user: user,
      message: "تم تسجيل الدخول بنجاح!"
    };
  } catch (error) {
    console.error("Error signing in:", error);
    return {
      success: false,
      error: error.code,
      message: getErrorMessage(error.code)
    };
  }
};

// Helper function to translate Firebase error messages to Arabic
const getErrorMessage = (errorCode) => {
  switch (errorCode) {
    case 'auth/email-already-in-use':
      return 'هذا البريد الإلكتروني مستخدم بالفعل';
    case 'auth/weak-password':
      return 'كلمة المرور ضعيفة جداً';
    case 'auth/invalid-email':
      return 'البريد الإلكتروني غير صحيح';
    case 'auth/user-not-found':
      return 'المستخدم غير موجود';
    case 'auth/wrong-password':
      return 'كلمة المرور غير صحيحة';
    case 'auth/too-many-requests':
      return 'تم تجاوز عدد المحاولات المسموح، حاول مرة أخرى لاحقاً';
    case 'auth/network-request-failed':
      return 'خطأ في الاتصال بالإنترنت';
    default:
      return 'حدث خطأ غير متوقع، حاول مرة أخرى';
  }
};

// Additional utility functions for data management
export const saveContactForm = async (formData) => {
  try {
    const contactRef = ref(database, 'contactForms');
    const newContactRef = push(contactRef);
    await set(newContactRef, {
      ...formData,
      submittedAt: new Date().toISOString(),
      status: 'new'
    });
    return { success: true, message: "تم إرسال الرسالة بنجاح!" };
  } catch (error) {
    console.error("Error saving contact form:", error);
    return { success: false, message: "حدث خطأ في إرسال الرسالة" };
  }
};

export const saveServiceRequest = async (serviceData) => {
  try {
    const serviceRef = ref(database, 'serviceRequests');
    const newServiceRef = push(serviceRef);
    await set(newServiceRef, {
      ...serviceData,
      requestedAt: new Date().toISOString(),
      status: 'pending'
    });
    return { success: true, message: "تم إرسال طلب الخدمة بنجاح!" };
  } catch (error) {
    console.error("Error saving service request:", error);
    return { success: false, message: "حدث خطأ في إرسال طلب الخدمة" };
  }
};

export const getUserData = async (uid) => {
  try {
    const userRef = ref(database, 'users/' + uid);
    const snapshot = await get(userRef);
    if (snapshot.exists()) {
      return { success: true, data: snapshot.val() };
    } else {
      return { success: false, message: "بيانات المستخدم غير موجودة" };
    }
  } catch (error) {
    console.error("Error getting user data:", error);
    return { success: false, message: "حدث خطأ في جلب البيانات" };
  }
};

export default app;
